import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:get/instance_manager.dart';
import 'package:get/route_manager.dart';
import 'package:qbank_bd/core/common/widgets/q_buttons.dart';
import 'package:qbank_bd/core/router/app_router.dart';
import 'package:qbank_bd/core/theme/theme_controller.dart';
import 'package:qbank_bd/feature/auth/view/login_screen.dart';
import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  await Supabase.initialize(
    url: 'https://srxnoaacjobsmjpbaypb.supabase.co',
    anonKey:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.OUNc0B3tAbYNvMzliwFu-Ikjhn6wzMUzwc57SsphIyY',
  );
  Get.put(ThemeController());
  Get.put(AuthController(), permanent: true);

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    final themeController = Get.find<ThemeController>();
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,

      theme: themeController.lightTheme,
      darkTheme: themeController.darkTheme,
      themeMode: themeController.themeMode.value,
      getPages: AppRoutes.routes,
      home: const RootScreen(),
    );
  }
}

class RootScreen extends StatelessWidget {
  const RootScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final AuthController authController = Get.find<AuthController>();

    return Obx(() {
      if (!authController.isConnected.value) {
        return const Scaffold(
          body: Center(child: Text("No network connection")),
        );
      }

      if (authController.user.value == null) {
        return LoginScreen();
      }

      return Scaffold(
        appBar: AppBar(title: Text(authController.user.value?.email ?? 'Home')),
        body: Center(
          child: QTextButton(text: 'Logout', onPressed: authController.signOut),
        ),
      );
    });
  }
}
