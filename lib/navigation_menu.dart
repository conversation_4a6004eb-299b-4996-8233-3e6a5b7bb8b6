// import 'package:flutter/material.dart';
// import 'package:font_awesome_flutter/font_awesome_flutter.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/core/utils/pallete.dart';
// import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';
// import 'package:qbank_bd/feature/course/view/screens/course_screen.dart';
// import 'package:qbank_bd/feature/qbanks/view/screens/qbanks_screen.dart';
// import 'package:qbank_bd/feature/leaderboard/view/screens/leaderboard_screen.dart';
// import 'package:qbank_bd/feature/home/<USER>/screens/home_screen.dart';
// import 'package:qbank_bd/feature/settings/view/screen/settings_screen.dart';
// import 'package:supabase_flutter/supabase_flutter.dart';

// class NavigationMenu extends StatefulWidget {
//   const NavigationMenu({super.key});

//   @override
//   State<NavigationMenu> createState() => _NavigationMenuState();
// }

// class _NavigationMenuState extends State<NavigationMenu> {
//   final supabase = Supabase.instance.client;
//   late final NavigationController controller;

//   @override
//   void initState() {
//     super.initState();
//     controller = Get.put(NavigationController());
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: GetX<NavigationController>(
//         builder:
//             (controller) => controller.screens[controller.selectedIndex.value],
//       ),
//       bottomNavigationBar: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Divider(height: 1, thickness: 1.2, color: Colors.grey.withAlpha(40)),
//           SafeArea(
//             top: false,
//             child: Container(
//               height: 60,
//               color: Theme.of(context).scaffoldBackgroundColor,
//               child: GetX<NavigationController>(
//                 builder:
//                     (controller) => Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceAround,
//                       children: [
//                         Expanded(
//                           child: _navItem(
//                             index: 0,
//                             icon: FontAwesomeIcons.house,
//                             filledIcon: FontAwesomeIcons.house,
//                             label: 'Home',
//                             controller: controller,
//                           ),
//                         ),
//                         Expanded(
//                           child: _navItem(
//                             index: 1,
//                             icon: FontAwesomeIcons.pencil,
//                             filledIcon: FontAwesomeIcons.pencil,
//                             label: 'Courses',
//                             controller: controller,
//                           ),
//                         ),
//                         Expanded(
//                           child: _navItem(
//                             index: 2,
//                             icon: FontAwesomeIcons.book,
//                             filledIcon: FontAwesomeIcons.book,
//                             label: 'Qbanks',
//                             controller: controller,
//                           ),
//                         ),
//                         Expanded(
//                           child: _navItem(
//                             index: 3,
//                             icon: FontAwesomeIcons.trophy,
//                             filledIcon: FontAwesomeIcons.trophy,
//                             label: 'Leaderboard',
//                             controller: controller,
//                           ),
//                         ),
//                         Expanded(
//                           child: _navItem(
//                             index: 4,
//                             icon: FontAwesomeIcons.gear,
//                             filledIcon: FontAwesomeIcons.gear,
//                             label: 'Settings',
//                             controller: controller,
//                           ),
//                         ),
//                       ],
//                     ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _navItem({
//     required int index,
//     required IconData icon,
//     required IconData filledIcon,
//     required String label,
//     required NavigationController controller,
//     double? iconSize = 20,
//   }) {
//     final isActive = controller.selectedIndex.value == index;
//     return InkWell(
//       onTap: () => controller.changePage(index),
//       splashColor: Colors.transparent,
//       highlightColor: Colors.transparent,
//       child: SizedBox(
//         width: double.infinity,
//         height: double.infinity,
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Icon(
//               isActive ? filledIcon : icon,
//               color: isActive ? Pallete.primary : Color(0xFF9AA0AD),
//               size: iconSize,
//             ),
//             const SizedBox(height: 4),
//             Text(
//               label,
//               textScaler: const TextScaler.linear(1.0),
//               style: TextStyle(
//                 color: isActive ? Pallete.primary : Colors.grey,
//                 fontSize: 10,
//                 fontWeight: FontWeight.w600,
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

// class NavigationController extends GetxController {
//   final user = Get.find<AuthController>().currentUser;
//   final Rx<int> selectedIndex = 0.obs;

//   @override
//   void onInit() {
//     super.onInit();
//     final userModel = Get.find<AuthController>().currentUser.value;
//     if (userModel != null && userModel.courses.isEmpty) {
//       selectedIndex.value = 1;
//     }
//   }

//   final screens = [
//     const PracticeScreen(),
//     CourseScreen(),
//     QbankScreen(),
//     const LeaderboardScreen(),
//     SettingsScreen(),
//   ];

//   void changePage(int index) {
//     selectedIndex.value = index;
//   }
// }
