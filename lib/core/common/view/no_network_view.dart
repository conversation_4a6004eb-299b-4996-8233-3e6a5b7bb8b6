// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/core/common/widgets/q_buttons.dart';
// import 'package:qbank_bd/core/utils/pallete.dart';
// import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';
// import 'package:qbank_bd/feature/settings/view/screen/downloads_screen.dart';

// class NoNetworkView extends StatelessWidget {
//   const NoNetworkView({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final authController = Get.find<AuthController>();

//     return Scaffold(
//       body: SafeArea(
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 24),
//           child: Column(
//             children: [
//               // Main content centered in the screen
//               Expanded(
//                 child: Center(
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: [
//                       Icon(
//                         Icons.wifi_off_rounded,
//                         size: 100,
//                         color: Theme.of(context).colorScheme.error,
//                       ),
//                       const SizedBox(height: 16),
//                       Text(
//                         'No Internet Connection',
//                         style: Theme.of(context).textTheme.headlineSmall,
//                       ),
//                       const SizedBox(height: 8),
//                       Text(
//                         'Please check your internet connection',
//                         style: Theme.of(
//                           context,
//                         ).textTheme.bodyLarge?.copyWith(color: Colors.grey),
//                       ),
//                       const SizedBox(height: 24),
//                       QTextButton(
//                         text: 'Try Again',
//                         onPressed: () => authController.checkConnectivity(),
//                         backgroundColor: Pallete.primary,
//                         textColor: Colors.white,
//                       ),
//                     ],
//                   ),
//                 ),
//               ),

//               // Downloads button at the bottom
//               Padding(
//                 padding: const EdgeInsets.only(bottom: 24),
//                 child: QExpandedButton(
//                   text: 'Go to Downloads',
//                   onPressed: () => Get.to(() => DownloadsScreen()),
//                   icon: const Icon(Icons.arrow_forward, size: 20),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
