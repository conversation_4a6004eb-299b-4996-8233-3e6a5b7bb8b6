// import 'package:flutter/foundation.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/core/common/model/user_model.dart';
// import 'package:supabase_flutter/supabase_flutter.dart';
// 
// class UserController extends GetxController {
//   final supabase = Supabase.instance.client;
//   final currentUser = Rxn<UserModel>();
//   RealtimeChannel? _userSubscription;
// 
//   @override
//   void onInit() {
//     super.onInit();
//     _initializeUser();
//     _subscribeToUserChanges();
//   }
// 
//   @override
//   void onClose() {
//     _userSubscription?.unsubscribe();
//     super.onClose();
//   }
// 
//   Future<void> _initializeUser() async {
//     try {
//       final userId = supabase.auth.currentUser?.id;
//       if (userId == null) return;
// 
//       final userData =
//           await supabase.from('users').select().eq('id', userId).single();
// 
//       currentUser.value = UserModel.fromMap(userData);
//     } catch (e) {
//       if (kDebugMode) {
//         print('Error initializing user: $e');
//       }
//     }
//   }
// 
//   void _subscribeToUserChanges() {
//     final userId = supabase.auth.currentUser?.id;
//     if (userId == null) return;
// 
//     _userSubscription =
//         supabase
//             .channel('public:users')
//             .onPostgresChanges(
//               event: PostgresChangeEvent.update,
//               schema: 'public',
//               table: 'users',
//               filter: PostgresChangeFilter(
//                 type: PostgresChangeFilterType.eq,
//                 column: 'id',
//                 value: userId,
//               ),
//               callback: (payload) async {
//                 if (kDebugMode) {
//                   print('Received real-time update: $payload');
//                 }
//                 currentUser.value = UserModel.fromMap(
//                   Map<String, dynamic>.from(payload.newRecord),
//                 );
//               },
//             )
//             .subscribe();
//   }
// }
