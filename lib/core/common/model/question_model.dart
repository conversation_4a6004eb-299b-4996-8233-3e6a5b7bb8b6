import 'dart:convert';

class QuestionModel {
  final int id;
  final String questionText;
  final List<String>? options;
  final String? explanation;
  final List<String> quizId;

  QuestionModel({
    required this.id,
    required this.questionText,
    this.options,
    this.explanation,
    required this.quizId,
  });

  QuestionModel copyWith({
    int? id,
    String? questionText,
    List<String>? options,
    String? explanation,
    List<String>? quizId,
  }) {
    return QuestionModel(
      id: id ?? this.id,
      questionText: questionText ?? this.questionText,
      options: options ?? this.options,
      explanation: explanation ?? this.explanation,
      quizId: quizId ?? this.quizId,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'questionText': questionText,
      'options': options != null ? jsonEncode(options) : null,
      'explanation': explanation,
      'quizId': jsonEncode(quizId),
    };
  }

  factory QuestionModel.fromMap(Map<String, dynamic> map) {
    List<String>? parseStringList(dynamic data) {
      if (data == null) return null;
      if (data is List) {
        return data.map((e) => e.toString()).toList();
      } else if (data is String) {
        try {
          return List<String>.from(jsonDecode(data));
        } catch (_) {
          return [data];
        }
      }
      return null;
    }

    return QuestionModel(
      id:
          map['id'] is int
              ? map['id']
              : int.tryParse(map['id'].toString()) ?? 0,
      questionText: map['questionText'] ?? '',
      options: parseStringList(map['options']),
      explanation: map['explanation'],
      quizId: parseStringList(map['quizId']) ?? [],
    );
  }
}
