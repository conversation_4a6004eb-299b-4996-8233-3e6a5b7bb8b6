import 'dart:convert';

class QuizModel {
  final String id;
  final String name;
  final int questionCount;
  final int year;
  final int duration;
  final String subtopicId;
  final DateTime? downloadedAt;

  QuizModel({
    required this.id,
    required this.name,
    required this.questionCount,
    required this.year,
    required this.duration,
    required this.subtopicId,
    this.downloadedAt,
  });

  QuizModel copyWith({
    String? id,
    String? name,
    int? questionCount,
    int? year,
    int? duration,
    String? subtopicId,
    DateTime? downloadedAt,
  }) {
    return QuizModel(
      id: id ?? this.id,
      name: name ?? this.name,
      questionCount: questionCount ?? this.questionCount,
      year: year ?? this.year,
      duration: duration ?? this.duration,
      subtopicId: subtopicId ?? this.subtopicId,
      downloadedAt: downloadedAt ?? this.downloadedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'questionCount': questionCount,
      'year': year,
      'duration': duration,
      'subtopicId': subtopicId,
      'downloadedAt': downloadedAt?.toIso8601String(),
    };
  }

  factory QuizModel.fromMap(Map<String, dynamic> map) {
    return QuizModel(
      id: map['id'],
      name: map['name'],
      questionCount: map['questionCount'],
      year: map['year'],
      duration: map['duration'],
      subtopicId: map['subtopicId'],
      downloadedAt:
          map['downloadedAt'] != null
              ? DateTime.parse(map['downloadedAt'])
              : null,
    );
  }
}
