import 'dart:convert';

class CardModel {
  final String title;
  final String bodyText;

  CardModel({required this.title, required this.bodyText});

  factory CardModel.fromMap(Map<String, dynamic> map) {
    return CardModel(
      title: map['title'] ?? '',
      bodyText: map['bodyText'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {'title': title, 'bodyText': bodyText};
  }

  factory CardModel.fromJson(String source) =>
      CardModel.fromMap(json.decode(source));

  String toJson() => json.encode(toMap());
}
