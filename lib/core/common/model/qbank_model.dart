import 'dart:convert';

class QbankModel {
  final String id;
  final String coverImage;
  final String name;
  final int questionCount;
  final bool hasSubtopic;
  final List<String> levelId;
  final String category;
  final String color;

  QbankModel({
    required this.id,
    required this.coverImage,
    required this.name,
    required this.questionCount,
    required this.hasSubtopic,
    required this.levelId,
    required this.category,
    required this.color,
  });

  /// Create a copy with modifications
  QbankModel copyWith({
    String? id,
    String? coverImage,
    String? name,
    int? questionCount,
    bool? hasSubtopic,
    List<String>? levelId,
    String? category,
    String? color,
  }) {
    return QbankModel(
      id: id ?? this.id,
      coverImage: coverImage ?? this.coverImage,
      name: name ?? this.name,
      questionCount: questionCount ?? this.questionCount,
      hasSubtopic: hasSubtopic ?? this.hasSubtopic,
      levelId: levelId ?? this.levelId,
      category: category ?? this.category,
      color: color ?? this.color, // Fixed: was missing ?? this.color
    );
  }

  /// From Map (e.g., from Supabase)
  factory QbankModel.fromMap(Map<String, dynamic> map) {
    return QbankModel(
      id: map['id'] ?? '',
      coverImage: map['coverImage'] ?? '',
      name: map['name'] ?? '',
      questionCount: (map['questionCount'] ?? 0) as int,
      // These fields aren't in your database response, so provide defaults
      hasSubtopic:
          (map['hasSubtopic'] ?? false) is bool
              ? map['hasSubtopic']
              : map['hasSubtopic'] == 'true',
      levelId:
          map['levelId'] != null
              ? List<String>.from(map['levelId'])
              : <String>[], // Empty list as default
      category:
          map['category'] ?? '', // This will be empty since it's not in DB
      color: map['color'] ?? '',
    );
  }

  /// To Map (for inserting/updating to Supabase)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'coverImage': coverImage,
      'name': name,
      'questionCount': questionCount,
      'hasSubtopic': hasSubtopic,
      'levelId': levelId,
      'category': category,
      'color': color, // Fixed: was missing color
    };
  }

  /// From JSON String (if needed)
  factory QbankModel.fromJson(String source) =>
      QbankModel.fromMap(Map<String, dynamic>.from(jsonDecode(source)));

  /// To JSON String
  String toJson() => jsonEncode(toMap());
}
