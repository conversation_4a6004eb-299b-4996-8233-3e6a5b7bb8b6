import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:get/get.dart';

class CircularProfileImage extends StatelessWidget {
  final String? imageUrl;
  final File? imageFile;
  final double radius;
  final Widget? badge;
  final VoidCallback? onTap;

  const CircularProfileImage({
    super.key,
    this.imageUrl,
    this.imageFile,
    this.radius = 40,
    this.badge,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          _buildAvatar(context),
          if (badge != null) Positioned(bottom: 0, right: 0, child: badge!),
        ],
      ),
    );
  }

  Widget _buildAvatar(BuildContext context) {
    if (imageFile != null) {
      // Use file image if available
      return CircleAvatar(
        radius: radius,
        backgroundImage: FileImage(imageFile!),
      );
    } else if (imageUrl != null && imageUrl!.isNotEmpty) {
      // Use CachedNetworkImage for network images with improved caching
      return CachedNetworkImage(
        imageUrl: imageUrl!,
        imageBuilder:
            (context, imageProvider) =>
                CircleAvatar(radius: radius, backgroundImage: imageProvider),
        placeholder: (context, url) => _buildPlaceholderAvatar(context),
        errorWidget: (context, url, error) => _buildPlaceholderAvatar(context),
        cacheKey: imageUrl,
        memCacheWidth: (radius * 2).toInt(),
        memCacheHeight: (radius * 2).toInt(),
        fadeInDuration: Duration.zero,
        fadeOutDuration: Duration.zero,
      );
    } else {
      // No image available, show placeholder
      return _buildPlaceholderAvatar(context);
    }
  }

  Widget _buildPlaceholderAvatar(BuildContext context) {
    return CircleAvatar(
      radius: radius,
      backgroundColor: Get.theme.colorScheme.onPrimary.withAlpha(20),
      child: Icon(Icons.person, size: radius * 1.4, color: Colors.white54),
    );
  }
}
