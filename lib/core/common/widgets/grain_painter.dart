import 'dart:math';

import 'package:flutter/material.dart';

class GrainPainter extends CustomPainter {
  final int density;
  final Color color;
  final int alpha;

  GrainPainter({
    this.density = 100,
    this.color = Colors.white,
    this.alpha = 50,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color.withAlpha(alpha)
          ..strokeWidth = 1.0
          ..style = PaintingStyle.fill;

    final random = Random();

    for (int i = 0; i < density; i++) {
      final dx = random.nextDouble() * size.width;
      final dy = random.nextDouble() * size.height;
      canvas.drawCircle(Offset(dx, dy), 0.5, paint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
