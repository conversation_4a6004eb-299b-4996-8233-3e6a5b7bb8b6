import 'package:shared_preferences/shared_preferences.dart';

class SharedPrefsHelper {
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _lastStreakUpdateKey = 'last_streak_update';

  static Future<void> setLoggedIn(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isLoggedInKey, value);
  }

  static Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isLoggedInKey) ?? false;
  }

  static Future<void> setLastStreakUpdate(DateTime date) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastStreakUpdateKey, date.toIso8601String());
  }

  static Future<DateTime?> getLastStreakUpdate() async {
    final prefs = await SharedPreferences.getInstance();
    final dateStr = prefs.getString(_lastStreakUpdateKey);
    if (dateStr == null) return null;
    return DateTime.tryParse(dateStr);
  }

  static Future<void> clear() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_isLoggedInKey);
    await prefs.remove(_lastStreakUpdateKey);
  }
}
