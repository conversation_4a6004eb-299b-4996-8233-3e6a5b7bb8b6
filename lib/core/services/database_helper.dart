import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static DatabaseHelper get instance => _instance;

  static Database? _database;

  DatabaseHelper._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, 'qbank.db');

    print('SQLite database path: $path');

    return await openDatabase(
      path,
      version: 2, // Bump version to trigger onUpgrade
      onCreate: _createDb,
      onUpgrade: (db, oldVersion, newVersion) async {
        if (oldVersion < 2) {
          // Create user table if upgrading from version 1
          await db.execute('''
            CREATE TABLE IF NOT EXISTS user(
              id TEXT PRIMARY KEY,
              name TEXT NOT NULL,
              level INTEGER NOT NULL,
              streak INTEGER NOT NULL,
              profileImage TEXT
            )
          ''');
        }
      },
    );
  }

  Future<void> _createDb(Database db, int version) async {
    // Create quizzes table
    await db.execute('''
      CREATE TABLE quizzes(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        questionCount INTEGER NOT NULL,
        year INTEGER NOT NULL,
        duration INTEGER NOT NULL,
        subtopicId TEXT NOT NULL,
        downloadedAt TEXT
      )
    ''');

    // Create questions table
    await db.execute('''
      CREATE TABLE questions(
        id INTEGER PRIMARY KEY,
        questionText TEXT NOT NULL,
        options TEXT,
        explanation TEXT,
        quizId TEXT NOT NULL
      )
    ''');

    // Create user table for caching user data
    await db.execute('''
      CREATE TABLE user(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        level INTEGER NOT NULL,
        streak INTEGER NOT NULL,
        profileImage TEXT
      )
    ''');
  }

  // Insert or update user in cache
  Future<void> insertOrUpdateUser(Map<String, dynamic> user) async {
    final db = await database;
    await db.insert(
      'user',
      user,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  // Get cached user
  Future<Map<String, dynamic>?> getUser() async {
    final db = await database;
    final result = await db.query('user', limit: 1);
    if (result.isNotEmpty) {
      return result.first;
    }
    return null;
  }

  // Delete cached user
  Future<void> deleteUser() async {
    final db = await database;
    await db.delete('user');
  }
}
