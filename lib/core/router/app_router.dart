import 'package:flutter/cupertino.dart';
import 'package:get/route_manager.dart';
import 'package:qbank_bd/core/common/view/no_network_view.dart';
import 'package:qbank_bd/feature/auth/view/login_screen.dart';
import 'package:qbank_bd/feature/auth/view/profile_setup_screen.dart';
import 'package:qbank_bd/feature/qbanks/view/screens/all_subject_view.dart';
import 'package:qbank_bd/feature/qbanks/view/screens/qbanks_screen.dart';
import 'package:qbank_bd/feature/qbanks/view/screens/profile_screen.dart';
import 'package:qbank_bd/feature/qbanks/view/screens/subtopic_screen.dart';
import 'package:qbank_bd/feature/question/view/screen/question_screen.dart';
import 'package:qbank_bd/feature/qbanks/view/screens/quiz_screen.dart';
import 'package:qbank_bd/feature/question/view/screen/result_screen.dart';
import 'package:qbank_bd/navigation_menu.dart';

class AppRoutes {
  static const String noNetwork = '/no-network';
  static const String login = '/login';
  static const String menu = '/menu';
  static const String allSubject = '/all-subject';
  static const String question = '/question-sreen';
  static const String quiz = '/quiz-screen';
  static const String result = '/result-screen';
  static const String subtopic = '/subtopic-screen';
  static const String home = '/home';
  static const String profile = '/profile';
  static const String profileSetup = '/profile-setup';

  static final routes = [
    //GetPage(name: noNetwork, page: () => const NoNetworkView()),
    GetPage(name: login, page: () => LoginScreen()),
    GetPage(name: menu, page: () => Text('Home')),
    GetPage(name: profileSetup, page: () => ProfileSetupScreen()),
    //GetPage(name: home, page: () => QbankScreen()),
    GetPage(
      name: allSubject,
      page: () => AllSubjectView(subjects: Get.arguments ?? []),
    ),
    // GetPage(
    //   name: question,
    //   page:
    //       () => QuestionScreen(
    //         subjectId: Get.arguments['subjectId'],
    //         quiz: Get.arguments['quiz'],
    //       ),
    // ),
    //GetPage(name: quiz, page: () => QuizScreen(subject: Get.arguments)),
    //GetPage(name: result, page: () => ResultScreen()),
    GetPage(
      name: subtopic,
      page:
          () => SubtopicScreen(
            subtopics: Get.arguments['subtopics'] ?? [],
            subject: Get.arguments['subject'],
          ),
    ),
    //GetPage(name: profile, page: () => ProfilePage()),
  ];
}
