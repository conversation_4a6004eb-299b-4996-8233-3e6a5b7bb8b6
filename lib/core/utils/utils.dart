import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

void showAlertDialog(
  String title,
  String content,

  Function() onCancel,
  Function() onConfirm, {
  bool isYesDestructive = true,
  bool isNoDestructive = false,
}) {
  Get.dialog(
    CupertinoAlertDialog(
      title: Text(title),
      content: Text(content),
      actions: [
        CupertinoDialogAction(
          isDestructiveAction: isNoDestructive,
          child: const Text("No"),
          onPressed: () => onCancel(),
        ),
        CupertinoDialogAction(
          isDestructiveAction: isYesDestructive,
          child: const Text("Yes"),
          onPressed: () {
            onConfirm();
          },
        ),
      ],
    ),
  );
}

void showCustomSnackbar({
  required String title,
  required String message,
  required bool isSuccess,
}) {
  Get.snackbar(
    title,
    message,
    snackPosition: SnackPosition.BOTTOM,
    backgroundColor: isSuccess ? Colors.green : Colors.red,
    colorText: Colors.white,
    margin: const EdgeInsets.only(bottom: 16.0, left: 8.0, right: 8.0),
    borderRadius: 8.0,
    dismissDirection: DismissDirection.horizontal,
    isDismissible: true,
    duration: const Duration(seconds: 4),
    animationDuration: const Duration(milliseconds: 300),
    forwardAnimationCurve: Curves.easeOutQuart,
    reverseAnimationCurve: Curves.easeInQuart,
    mainButton: TextButton(
      onPressed: () => Get.back(),
      child: const Text(
        'Dismiss',
        style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
      ),
    ),
  );
}

Future<File?> pickImageFromGallery() async {
  try {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);

    if (pickedFile == null) {
      return null;
    }

    final imageFile = File(pickedFile.path);

    // Check if the file is an image
    final fileExtension = path.extension(imageFile.path).toLowerCase();
    if (!['.jpg', '.jpeg', '.png', '.webp', '.heic'].contains(fileExtension)) {
      showCustomSnackbar(
        title: 'Invalid File',
        message: 'Please select a valid image file',
        isSuccess: false,
      );
      return null;
    }

    // Compress the image
    return await compressImage(imageFile);
  } catch (e) {
    debugPrint('Error picking image: $e');
    showCustomSnackbar(
      title: 'Error',
      message: 'Failed to pick image: $e',
      isSuccess: false,
    );
    return null;
  }
}

void showCupertinoLevelSelector({
  required BuildContext context,
  required String currentLevel,
  required Function(String) onLevelSelected,
}) {
  showCupertinoModalPopup(
    context: context,
    builder:
        (BuildContext context) => CupertinoActionSheet(
          actions:
              availableLevels
                  .map(
                    (item) => CupertinoActionSheetAction(
                      onPressed: () {
                        onLevelSelected(item);
                        Navigator.pop(context);
                      },
                      child: Text(item),
                    ),
                  )
                  .toList(),
          cancelButton: CupertinoActionSheetAction(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ),
  );
}

Future<File> compressImage(File file) async {
  try {
    final tempDir = await getTemporaryDirectory();
    final fileName = path.basename(file.path);
    final fileExtension = path.extension(fileName).toLowerCase();

    // Ensure proper file extension for compression
    String targetPath;
    if (fileExtension == '.jpg' || fileExtension == '.jpeg') {
      // Keep original extension if it's already jpg/jpeg
      targetPath = '${tempDir.path}/$fileName';
    } else {
      // Force .jpg extension for other image types
      final nameWithoutExtension = path.basenameWithoutExtension(fileName);
      targetPath = '${tempDir.path}/$nameWithoutExtension.jpg';
    }

    final result = await FlutterImageCompress.compressAndGetFile(
      file.path,
      targetPath,
      quality: 20,
      minWidth: 256,
      minHeight: 256,
      rotate: 0,
      format: CompressFormat.jpeg,
    );

    return File(result?.path ?? file.path);
  } catch (e) {
    debugPrint('Image compression failed: $e');
    return file; // Return original file if compression fails
  }
}

// Level mapping
final Map<String, String> levelMapping = {
  'BCS': 'bcs',
  'HSC Science': 'hsc_science',
  'HSC Commerce': 'hsc_commerce',
  'HSC Arts': 'hsc_arts',
  'SSC Science': 'ssc_science',
  'SSC Commerce': 'ssc_commerce',
  'SSC Arts': 'ssc_arts',
};

// Reverse level mapping
final Map<String, String> reverseLevelMapping = {
  'bcs': 'BCS',
  'hsc_science': 'HSC Science',
  'hsc_commerce': 'HSC Commerce',
  'hsc_arts': 'HSC Arts',
  'ssc_science': 'SSC Science',
  'ssc_commerce': 'SSC Commerce',
  'ssc_arts': 'SSC Arts',
};

// List of available levels
final List<String> availableLevels = [
  'BCS',
  'HSC Science(BV)',
  'HSC Science(EV)',
  'HSC Commerce',
  'HSC Arts',
  'SSC Science',
  'SSC Commerce',
  'SSC Arts',
];

int intLevel(levelString) {
  switch (levelString) {
    case 'HSC Science(BV)':
      return 0;
    case 'HSC Science(EV)':
      return 1;
    case 'HSC Commerce':
      return 2;
    case 'HSC Arts':
      return 3;
    case 'SSC Science':
      return 4;
    case 'SSC Commerce':
      return 5;
    case 'SSC Arts':
      return 6;
    case 'BCS':
      return 7;
    default:
      return -1; // Invalid level
  }
}

String stringLevel(levelInt) {
  switch (levelInt) {
    case 0:
      return 'HSC Science(BV)';
    case 1:
      return 'HSC Science(EV)';
    case 2:
      return 'HSC Commerce';
    case 3:
      return 'HSC Arts';
    case 4:
      return 'SSC Science';
    case 5:
      return 'SSC Commerce';
    case 6:
      return 'SSC Arts';
    case 7:
      return 'BCS';
    default:
      return 'Unknown'; // Invalid level
  }
}

Color hexToColor(String hex) {
  hex = hex.replaceAll('#', '');
  if (hex.length == 6) hex = 'FF$hex'; // Add full opacity
  return Color(int.parse('0x$hex'));
}
