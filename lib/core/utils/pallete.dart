import 'package:flutter/material.dart';

class Pallete {
  // Primary
  static isDarkMode(BuildContext context) =>
      Theme.of(context).brightness == Brightness.dark;

  static const Color primary = Color(0xFF5D5BDB);

  // Light Theme Colors
  static const Color lightBackground = Color(0xFFF0F4F7);
  static const Color lightCardBackground = Color(0xFFFFFFFF);
  static const Color lightPrimaryText = Color(0xFF121928);
  static const Color lightSecondaryText = Color(0xFF333333);
  static const Color lightCaption = Color(0xFF666666);

  // Dark Theme Colors
  static const Color darkBackground = Color(0xFF121928);
  static const Color darkCardBackground = Color(0xFF1F2836);
  static const Color darkPrimaryText = Color(0xFFF0F4F7);
  static const Color darkSecondarytext = Color(0xFFC1C1C1);
  static const Color darkCaption = Color(0xFF999999);
}
