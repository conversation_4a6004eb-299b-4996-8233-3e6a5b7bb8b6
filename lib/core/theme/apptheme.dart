// import 'package:flutter/material.dart';
// import 'package:qbank_bd/core/utils/pallete.dart';

// class AppTheme {
//   static final ThemeData lightTheme = ThemeData(
//     brightness: Brightness.light,
//     primaryColor: Pallete.primary,
//     scaffoldBackgroundColor: Pallete.lightBackground,
//     colorScheme: ColorScheme.light(
//       primary: Pallete.primary,
//       surface: Pallete.lightCardBackground,
//       onPrimary: Colors.black,
//       onSurface: const Color(0xFF333333),
//     ),
//     textTheme: ThemeData.light().textTheme.apply(
//       bodyColor: const Color(0xFF1A1A1A),
//       displayColor: const Color(0xFF1A1A1A),
//     ),
//     inputDecorationTheme: InputDecorationTheme(
//       filled: true,
//       fillColor: const Color(0xFFF1F1F1),
//       border: OutlineInputBorder(
//         borderRadius: BorderRadius.circular(12),
//         borderSide: BorderSide.none,
//       ),
//     ),
//     elevatedButtonTheme: ElevatedButtonThemeData(
//       style: ElevatedButton.styleFrom(
//         backgroundColor: Pallete.primary,
//         foregroundColor: Colors.white,
//         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
//         elevation: 0,
//       ),
//     ),
//   );
//
//   static final ThemeData darkTheme = ThemeData(
//     brightness: Brightness.dark,
//     primaryColor: Pallete.primary,
//     scaffoldBackgroundColor: Pallete.darkBackground,
//     colorScheme: ColorScheme.dark(
//       primary: Pallete.primary,
//       surface: Pallete.dartCardBackground,
//       onPrimary: Colors.white,
//       onSurface: Colors.white,
//     ),
//     textTheme: ThemeData.dark().textTheme.apply(
//       bodyColor: Colors.white,
//       displayColor: Colors.white,
//     ),
//     inputDecorationTheme: InputDecorationTheme(
//       filled: true,
//       fillColor: Pallete.dartCardBackground,
//       border: OutlineInputBorder(
//         borderRadius: BorderRadius.circular(12),
//         borderSide: BorderSide.none,
//       ),
//     ),
//     elevatedButtonTheme: ElevatedButtonThemeData(
//       style: ElevatedButton.styleFrom(
//         backgroundColor: Pallete.primary,
//         foregroundColor: Colors.white,
//         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
//         elevation: 0,
//       ),
//     ),
//   );
// }
