import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qbank_bd/core/utils/pallete.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeController extends GetxController {
  var themeMode = ThemeMode.system.obs;

  final ThemeData lightTheme = ThemeData(
    brightness: Brightness.light,
    scaffoldBackgroundColor: Pallete.lightBackground,
    cardColor: Pallete.lightCardBackground,
    primaryColor: Pallete.primary,
    appBarTheme: AppBarTheme(
      backgroundColor: Pallete.lightBackground,
      foregroundColor: Pallete.lightPrimaryText,
      elevation: 0,
      iconTheme: const IconThemeData(color: Pallete.lightPrimaryText),
    ),
    colorScheme: ColorScheme.light(
      primary: Pallete.primary,
      surface: Pallete.lightCardBackground,
      onPrimary: Pallete.lightPrimaryText,
      onSurface: Pallete.darkCardBackground,
    ),
  );

  final ThemeData darkTheme = ThemeData(
    brightness: Brightness.dark,
    scaffoldBackgroundColor: Pallete.darkBackground,
    cardColor: Pallete.darkCardBackground,
    primaryColor: Pallete.primary,
    appBarTheme: AppBarTheme(
      backgroundColor: Pallete.darkBackground,
      foregroundColor: Pallete.darkPrimaryText,
      elevation: 0,
      iconTheme: const IconThemeData(color: Pallete.darkPrimaryText),
    ),
    colorScheme: ColorScheme.dark(
      primary: Pallete.primary,
      surface: Pallete.darkCardBackground,
      onPrimary: Pallete.lightBackground,
      onSurface: Pallete.lightBackground,
    ),
  );

  @override
  void onInit() {
    super.onInit();
    _loadTheme();
  }

  void _loadTheme() async {
    final prefs = await SharedPreferences.getInstance();
    final themeString = prefs.getString('themeMode') ?? 'system';

    switch (themeString) {
      case 'light':
        setThemeMode(ThemeMode.light);
        break;
      case 'dark':
        setThemeMode(ThemeMode.dark);
        break;
      default:
        setThemeMode(ThemeMode.system);
    }
  }

  void setThemeMode(ThemeMode mode) async {
    themeMode.value = mode;

    final prefs = await SharedPreferences.getInstance();
    String themeString;

    switch (mode) {
      case ThemeMode.light:
        themeString = 'light';
        Get.changeTheme(lightTheme);
        Get.changeThemeMode(ThemeMode.light);
        break;
      case ThemeMode.dark:
        themeString = 'dark';
        Get.changeTheme(darkTheme);
        Get.changeThemeMode(ThemeMode.dark);
        break;
      default:
        themeString = 'system';
        Get.changeThemeMode(ThemeMode.system);
        break;
    }

    await prefs.setString('themeMode', themeString);
    Get.forceAppUpdate();
  }
}
