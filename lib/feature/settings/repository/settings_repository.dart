import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import 'package:qbank_bd/core/common/model/question_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SettingsRepository {
  final _client = Supabase.instance.client;

  final defaultAvatars = [
    'https://srxnoaacjobsmjpbaypb.supabase.co/storage/v1/object/sign/avatars/default%20avatars/3551739.jpg?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************.zBy_XzMjvj0XKgK7BTBkQkaSjaGAu09nEX8MvHVFlb4',
    'https://srxnoaacjobsmjpbaypb.supabase.co/storage/v1/object/sign/avatars/default%20avatars/7309681.jpg?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************.HabhHFVr5jlIlkDegw_tGpZEg0j51rLGhkwa6a0sYKc',
    'https://srxnoaacjobsmjpbaypb.supabase.co/storage/v1/object/sign/avatars/default%20avatars/9440461.jpg?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************.Fn_RD1bSRzX-Df9gmGH8GXMP4KzOG5D94hP49TN64cM',
  ];
  Future<void> updateUserProfile({
    required String userId,
    required String name,
    required String? phone,
    required String level,
    required File? profileImage,
    required String currentImageUrl,
  }) async {
    String profileImageUrl = currentImageUrl;

    try {
      // Handle profile image update
      if (profileImage != null) {
        // Delete existing image if it exists and is not a default avatar
        if (profileImageUrl.isNotEmpty &&
            profileImageUrl.contains('/avatars/') &&
            !profileImageUrl.contains('default%20avatars')) {
          // Extract filename from URL
          final fileName = profileImageUrl.split('/').last.split('?').first;
          try {
            await _client.storage.from('avatars').remove([fileName]);
          } catch (e) {
            // Continue with upload even if delete fails
          }
        }
        // Upload compressed image to the avatars bucket
        final fileExtension = path
            .extension(profileImage.path)
            .replaceAll('.', '');
        final fileName = '$userId.$fileExtension';

        try {
          await _client.storage
              .from('avatars')
              .upload(
                fileName,
                profileImage,
                fileOptions: const FileOptions(
                  cacheControl: '3600',
                  upsert: true,
                ),
              );

          // Get the public URL for the uploaded image
          final url = await _client.storage
              .from('avatars')
              .createSignedUrl(
                fileName,
                60 * 60 * 24 * 7,
              ); // 7-day valid signed URL

          try {
            final testResponse = await http.get(Uri.parse(url));
            if (testResponse.statusCode == 200) {
              profileImageUrl = url;
            } else {
              // Use the URL anyway since we know the upload was successful
              profileImageUrl = url;
            }
          } catch (e) {
            // Use the URL anyway since we know the upload was successful
            profileImageUrl = url;
          }
        } catch (e) {
          throw Exception('Failed to upload profile image: $e');
        }
      }

      // Update user data in database
      await _client
          .from('users')
          .update({
            'name': name,
            'phone': phone,
            'level': level,
            'profileImage': profileImageUrl,
          })
          .eq('id', userId);
    } catch (e) {
      throw Exception('Failed to update user profile: $e');
    }
  }

  Future<List<QuestionModel>> fetchQuestionsPaginated(
    List<String> questionIds,
    int page,
    int limit,
  ) async {
    try {
      if (questionIds.isEmpty) {
        return [];
      }

      // Calculate start and end indices for pagination
      final startIndex = page * limit;
      final endIndex = startIndex + limit;

      // Get the subset of question IDs for this page
      final pageQuestionIds =
          questionIds.length > startIndex
              ? questionIds.sublist(
                startIndex,
                endIndex < questionIds.length ? endIndex : questionIds.length,
              )
              : [];

      if (pageQuestionIds.isEmpty) {
        return [];
      }

      // Convert string IDs to integers for the query
      final intIds = pageQuestionIds.map((id) => int.parse(id)).toList();

      final response = await _client
          .from('questions')
          .select()
          .inFilter('id', intIds)
          .order('id', ascending: false);

      return (response as List)
          .map((item) => QuestionModel.fromMap(item as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Error fetching questions: $e');
      return [];
    }
  }
}
