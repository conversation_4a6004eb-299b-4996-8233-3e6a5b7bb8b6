// import 'dart:io';
// import 'package:get/get.dart';
// import 'package:qbank_bd/core/utils/utils.dart';
// import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';
// import 'package:qbank_bd/feature/settings/repository/settings_repository.dart';

// class SettingsViewModel extends GetxController {
//   final _repo = SettingsRepository();
//   final _authController = Get.find<AuthController>();

//   final isLoading = false.obs;
//   final errorMessage = ''.obs;

//   // Add these properties for UI state management
//   final selectedLevel = 'HSC Science'.obs;
//   final nameText = ''.obs;
//   final phoneText = ''.obs;
//   final imageFile = Rxn<File>();

//   @override
//   void onInit() {
//     super.onInit();
//     loadUserData();
//   }

//   void loadUserData() {
//     final user = _authController.currentUser.value;
//     if (user != null) {
//       nameText.value = user.name;

//       // Format phone number to remove +88 prefix for display
//       if (user.phone != null && user.phone!.startsWith('+88')) {
//         phoneText.value = user.phone!.substring(3);
//       } else {
//         phoneText.value = user.phone ?? '';
//       }

//       // Set selected level from user data
//       final userLevel = user.level;
//       if (reverseLevelMapping.containsKey(userLevel)) {
//         selectedLevel.value = reverseLevelMapping[userLevel]!;
//       }
//     }
//   }

//   void setSelectedLevel(String level) {
//     selectedLevel.value = level;
//   }

//   void setImageFile(File? file) {
//     imageFile.value = file;
//   }

//   Future<bool> updateUserProfile({
//     required String name,
//     required String? phone,
//     required String level,
//     required File? profileImage,
//     String? userId,
//     String? currentImageUrl,
//   }) async {
//     isLoading.value = true;
//     errorMessage.value = '';

//     try {
//       // For existing users, get from auth controller
//       String actualUserId;
//       String actualImageUrl = '';

//       if (userId != null) {
//         // For new user registration
//         actualUserId = userId;
//       } else {
//         // For existing user profile update
//         final user = _authController.currentUser.value;
//         if (user == null) {
//           throw Exception('User not logged in');
//         }
//         actualUserId = user.id;
//         actualImageUrl = user.profileImage;
//       }

//       await _repo.updateUserProfile(
//         userId: actualUserId,
//         name: name,
//         phone: phone,
//         level: level,
//         profileImage: profileImage,
//         currentImageUrl: currentImageUrl ?? actualImageUrl,
//       );
//       return true;
//     } catch (e) {
//       errorMessage.value = 'Failed to update profile: $e';
//       return false;
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   bool validateInputs() {
//     if (nameText.value.trim().isEmpty) {
//       errorMessage.value = 'Please enter your name';
//       return false;
//     }
//     return true;
//   }

//   String formatPhoneNumber() {
//     return phoneText.value.trim().isNotEmpty
//         ? '+88${phoneText.value.trim()}'
//         : '';
//   }

//   String getLevelValue() {
//     return levelMapping[selectedLevel.value] ?? 'hsc_science';
//   }

//   String getCurrentImageUrl() {
//     return _authController.currentUser.value?.profileImage ?? '';
//   }

//   // Add method to get default avatars if needed
//   List<String> getDefaultAvatars() {
//     return _repo.defaultAvatars;
//   }

//   Future<void> pickImage() async {
//     try {
//       final pickedFile = await pickImageFromGallery();
//       if (pickedFile != null) {
//         imageFile.value = pickedFile;
//       }
//     } catch (e) {
//       errorMessage.value = 'Error picking image: $e';
//     }
//   }
// }
