// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/core/common/model/question_model.dart';
// import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';
// import 'package:qbank_bd/feature/question/repository/question_repository.dart';
// import 'package:qbank_bd/feature/settings/repository/settings_repository.dart';

// class LikedQuestionsViewModel extends GetxController {
//   final _questionRepository = QuestionRepository();
//   final _settingsRepository = SettingsRepository();
//   final authController = Get.find<AuthController>();

//   final questions = <QuestionModel>[].obs;
//   final isLoading = false.obs;
//   final isLoadingMore = false.obs;
//   final likedQuestionIds = <String>[].obs;
//   final scrollController = ScrollController();

//   int _page = 0;
//   bool _hasMore = true;
//   final int _limit = 10;

//   @override
//   void onInit() {
//     super.onInit();
//     fetchLikedQuestionIds();

//     scrollController.addListener(() {
//       if (scrollController.position.pixels >=
//           scrollController.position.maxScrollExtent - 200) {
//         loadMoreQuestions();
//       }
//     });
//   }

//   @override
//   void onClose() {
//     scrollController.dispose();
//     super.onClose();
//   }

//   Future<void> fetchLikedQuestionIds() async {
//     try {
//       isLoading.value = true;
//       final userId = authController.currentUser.value!.id;
//       final ids = await _questionRepository.getUserLikedQuestions(userId);
//       likedQuestionIds.assignAll(ids);

//       // After getting IDs, load the first page of questions
//       await loadInitialQuestions();
//     } catch (e) {
//       print('Error fetching liked question IDs: $e');
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   Future<void> loadInitialQuestions() async {
//     _page = 0;
//     _hasMore = true;
//     questions.clear();

//     if (likedQuestionIds.isEmpty) {
//       return;
//     }

//     try {
//       isLoading.value = true;
//       final result = await _settingsRepository.fetchQuestionsPaginated(
//         likedQuestionIds,
//         _page,
//         _limit,
//       );

//       questions.addAll(result);
//       _hasMore = result.length == _limit;
//     } catch (e) {
//       print('Error loading initial questions: $e');
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   Future<void> loadMoreQuestions() async {
//     if (!_hasMore || isLoadingMore.value) return;

//     isLoadingMore.value = true;
//     _page++;

//     try {
//       final result = await _settingsRepository.fetchQuestionsPaginated(
//         likedQuestionIds,
//         _page,
//         _limit,
//       );

//       questions.addAll(result);
//       _hasMore = result.length == _limit;
//     } catch (e) {
//       print('Error loading more questions: $e');
//       _page--;
//     } finally {
//       isLoadingMore.value = false;
//     }
//   }

//   Future<void> unlikeQuestion(int questionId) async {
//     final questionIdStr = questionId.toString();
//     try {
//       if (likedQuestionIds.contains(questionIdStr)) {
//         // Remove from current list first for immediate UI update
//         questions.removeWhere((q) => q.id == questionId);

//         // Then remove from liked questions in backend
//         likedQuestionIds.remove(questionIdStr);
//         await _questionRepository.updateLikedQuestions(
//           authController.currentUser.value!.id,
//           likedQuestionIds,
//         );
//       }
//     } catch (e) {
//       print('Error unliking question: $e');
//     }
//   }

//   bool get hasMoreData => _hasMore;
//   void handleUnlike(int questionId) {
//     unlikeQuestion(questionId);
//   }

//   bool isQuestionLiked(int questionId) {
//     return likedQuestionIds.contains(questionId.toString());
//   }

//   // Future<void> toggleLikeQuestion(int questionId) async {
//   //   final questionIdStr = questionId.toString();
//   //   try {
//   //     if (likedQuestionIds.length >= 100) {
//   //       // Remove the oldest item (first in the list)
//   //       likedQuestionIds.removeAt(0);
//   //     }
//   //     likedQuestionIds.remove(questionIdStr);
//   //     await _questionRepository.updateLikedQuestions(
//   //       authController.currentUser.value!.id,
//   //       likedQuestionIds,
//   //     );
//   //   } catch (e) {
//   //     print('Error toggling like: $e');
//   //     showErrorSnackbar('Error', "Couldn't like or unlike the question");
//   //   }
//   // }
// }
