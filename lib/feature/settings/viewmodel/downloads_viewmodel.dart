// import 'package:get/get.dart';
// import 'package:qbank_bd/core/common/model/quiz_model.dart';
// import 'package:qbank_bd/core/utils/utils.dart';
// import 'package:qbank_bd/core/services/database_helper.dart';
// import 'package:qbank_bd/feature/qbanks/viewmodel/quiz_viewmodel.dart';

// class DownloadsViewModel extends GetxController {
//   final downloadedQuizzes = <QuizModel>[].obs;
//   final isLoading = false.obs;

//   @override
//   void onInit() {
//     super.onInit();
//     fetchDownloadedQuizzes();
//   }

//   Future<void> fetchDownloadedQuizzes() async {
//     try {
//       isLoading.value = true;
//       final db = await DatabaseHelper.instance.database;

//       final quizzes = await db.query(
//         'quizzes',
//         where: 'downloadedAt IS NOT NULL',
//         orderBy: 'downloadedAt DESC',
//       );

//       downloadedQuizzes.assignAll(
//         quizzes.map((map) => QuizModel.fromMap(map)).toList(),
//       );
//     } catch (e) {
//       // Error handling without print
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   Future<void> deleteDownloadedQuiz(String quizId) async {
//     try {
//       final db = await DatabaseHelper.instance.database;

//       // Show confirmation dialog
//       showAlertDialog(
//         'Remove Download',
//         'Are you sure you want to delete this downloaded quiz?',
//         () {
//           Get.back();
//         },
//         () async {
//           Get.back();

//           try {
//             // Delete the quiz and its questions
//             await db.transaction((txn) async {
//               // Delete the quiz
//               await txn.delete('quizzes', where: 'id = ?', whereArgs: [quizId]);

//               // Delete associated questions
//               await txn.delete(
//                 'questions',
//                 where: 'quizId LIKE ?',
//                 whereArgs: ['%$quizId%'],
//               );
//             });

//             // Refresh the list
//             await fetchDownloadedQuizzes();

//             // Try to update the quizViewModel's quiz list if it exists
//             try {
//               final quizViewModel = Get.find<QuizViewModel>();
//               // Force refresh of any quiz cards that might be displaying this quiz
//               final index = quizViewModel.quizzes.indexWhere(
//                 (q) => q.id == quizId,
//               );
//               if (index != -1) {
//                 quizViewModel.quizzes.refresh();
//               }
//             } catch (e) {
//               // quizViewModel not found - silent handling
//             }
//           } catch (e) {
//             showCustomSnackbar(
//               title: 'Error',
//               message: 'Failed to delete quiz: $e',
//               isSuccess: false,
//             );
//           }
//         },
//       );
//     } catch (e) {
//       // Silent error handling
//     }
//   }
// }
