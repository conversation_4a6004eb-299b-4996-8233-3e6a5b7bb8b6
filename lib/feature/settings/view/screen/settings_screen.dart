// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/core/utils/utils.dart';
// import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';
// import 'package:qbank_bd/feature/settings/view/screen/liked_questions_screen.dart';
// import 'package:qbank_bd/feature/settings/view/screen/edit_profile_screen.dart';
// import 'package:qbank_bd/feature/settings/view/screen/reported_questions_screen.dart';
// import 'package:qbank_bd/feature/settings/view/screen/downloads_screen.dart';
// import 'package:qbank_bd/feature/settings/view/widgets/settings_list_tile.dart';
// import 'package:qbank_bd/core/common/widgets/circular_profile_image.dart';
// import 'package:qbank_bd/core/common/widgets/q_buttons.dart';
// import 'package:qbank_bd/core/utils/pallete.dart';
// import 'package:font_awesome_flutter/font_awesome_flutter.dart';

// class SettingsScreen extends StatelessWidget {
//   SettingsScreen({super.key});
//   final AuthController authController = Get.find();

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: SafeArea(
//         child: ListView(
//           padding: const EdgeInsets.all(16),
//           children: [
//             const SizedBox(height: 16),
//             // Profile section with rounded background
//             Container(
//               padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
//               decoration: BoxDecoration(
//                 color: Get.theme.colorScheme.surface,
//                 borderRadius: BorderRadius.circular(16),
//               ),
//               child: Column(
//                 children: [
//                   Obx(() {
//                     return CircularProfileImage(
//                       imageUrl: authController.currentUser.value?.profileImage,
//                       radius: 50,
//                     );
//                   }),
//                   SizedBox(height: 16),
//                   Obx(() {
//                     return Text(
//                       authController.currentUser.value?.name ?? "Guest User",
//                       style: TextStyle(
//                         fontSize: MediaQuery.of(context).size.width * 0.055,
//                         fontWeight: FontWeight.bold,
//                         color: Get.theme.colorScheme.onSurface,
//                       ),
//                       textAlign: TextAlign.center,
//                       overflow: TextOverflow.ellipsis,
//                       maxLines: 1,
//                     );
//                   }),
//                   SizedBox(height: 8),
//                   Obx(() {
//                     final levelKey =
//                         authController.currentUser.value?.level ?? "";
//                     final displayLevel =
//                         reverseLevelMapping[levelKey] ?? "Level not found";
//                     return Container(
//                       padding: const EdgeInsets.all(
//                         4,
//                       ).copyWith(left: 12, right: 12),
//                       decoration: BoxDecoration(
//                         color: Pallete.primary.withAlpha(10),
//                         borderRadius: BorderRadius.circular(30),
//                       ),
//                       child: Text(
//                         displayLevel,
//                         style: TextStyle(
//                           color: Pallete.primary,
//                           fontWeight: FontWeight.w600,
//                         ),
//                       ),
//                     );
//                   }),
//                   SizedBox(height: 16),
//                   QTextButton(
//                     text: 'Edit Profile',
//                     onPressed: () => Get.to(() => EditProfileScreen()),
//                     backgroundColor: Pallete.primary,
//                     textColor: Colors.white,
//                   ),
//                 ],
//               ),
//             ),
//             const SizedBox(height: 30),
//             SettingsListTile(
//               icon: FontAwesomeIcons.solidHeart,
//               title: 'Liked Questions',
//               onTap: () {
//                 Get.to(() => LikedQuestionsScreen());
//               },
//             ),
//             SettingsListTile(
//               icon: FontAwesomeIcons.solidFlag,
//               title: 'Reported Questions',
//               onTap: () {
//                 Get.to(() => ReportedQuestionsScreen());
//               },
//             ),

//             SettingsListTile(
//               icon: FontAwesomeIcons.gift,
//               title: 'Gifts',
//               onTap: () {},
//             ),

//             SettingsListTile(
//               icon: FontAwesomeIcons.download,
//               title: 'Downloads',
//               onTap: () {
//                 Get.to(() => DownloadsScreen());
//               },
//             ),
//             SettingsListTile(
//               icon: FontAwesomeIcons.headset,
//               title: 'Support',
//               onTap: () {},
//             ),
//             SettingsListTile(
//               icon: FontAwesomeIcons.circleInfo,
//               title: 'About',
//               onTap: () {
//                 //Get.to(() => AboutScreen());
//               },
//             ),
//             SettingsListTile(
//               icon: FontAwesomeIcons.shield,
//               title: 'Privacy Policy',
//               onTap: () {},
//             ),

//             SettingsListTile(
//               icon: FontAwesomeIcons.rightFromBracket,
//               title: 'Logout',
//               iconColor: Colors.pink,
//               onTap: () {
//                 showAlertDialog(
//                   'Logout',
//                   'Are you sure you want to logout?',
//                   () {
//                     Get.back();
//                   },
//                   () async {
//                     Get.back();
//                     await authController.signOut();
//                   },
//                 );
//               },
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
