// import 'package:flutter/material.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/core/common/widgets/textfield.dart';
// import 'package:qbank_bd/core/common/widgets/circular_profile_image.dart';
// import 'package:qbank_bd/core/common/widgets/q_buttons.dart';
// import 'package:qbank_bd/core/utils/pallete.dart';
// import 'package:qbank_bd/core/utils/utils.dart';
// import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';
// import 'package:qbank_bd/feature/qbanks/viewmodel/qbank_viewmodel.dart';
// import 'package:qbank_bd/feature/leaderboard/viewmodel/leaderboard_viewmodel.dart';
// import 'package:qbank_bd/feature/settings/viewmodel/settings_viewmodel.dart';

// class EditProfileScreen extends StatefulWidget {
//   const EditProfileScreen({super.key});

//   @override
//   State<EditProfileScreen> createState() => _EditProfileScreenState();
// }

// class _EditProfileScreenState extends State<EditProfileScreen> {
//   final SettingsViewModel settingsViewModel = Get.put(SettingsViewModel());
//   final AuthController authController = Get.find<AuthController>();

//   final TextEditingController nameController = TextEditingController();
//   final TextEditingController phoneController = TextEditingController();

//   @override
//   void initState() {
//     super.initState();
//     // Initialize controllers with values from ViewModel
//     nameController.text = settingsViewModel.nameText.value;
//     phoneController.text = settingsViewModel.phoneText.value;

//     // Listen for changes in the ViewModel
//     ever(settingsViewModel.nameText, (value) => nameController.text = value);
//     ever(settingsViewModel.phoneText, (value) => phoneController.text = value);
//   }

//   @override
//   void dispose() {
//     nameController.dispose();
//     phoneController.dispose();
//     super.dispose();
//   }

//   void _updateProfile() async {
//     // Update ViewModel with current text values
//     settingsViewModel.nameText.value = nameController.text;
//     settingsViewModel.phoneText.value = phoneController.text;

//     // Validate inputs using ViewModel
//     if (!settingsViewModel.validateInputs()) {
//       return;
//     } else {
//       // Format phone number using ViewModel
//       final phone = settingsViewModel.formatPhoneNumber();

//       // Get level value from ViewModel
//       final levelValue = settingsViewModel.getLevelValue();

//       // Update profile using ViewModel
//       final success = await settingsViewModel.updateUserProfile(
//         name: nameController.text.trim(),
//         phone: phone.isNotEmpty ? phone : null,
//         level: levelValue,
//         profileImage: settingsViewModel.imageFile.value,
//         currentImageUrl: settingsViewModel.getCurrentImageUrl(),
//       );

//       if (success) {
//         await Get.find<AuthController>().refreshUserData();
//         await Get.find<QbankViewModel>().initializeData();
//         await Get.find<LeaderboardViewModel>().initializeData();
//         Get.back();
//       } else {
//         showCustomSnackbar(
//           title: 'Error',
//           message:
//               'Failed to update profile: ${settingsViewModel.errorMessage.value}',
//           isSuccess: false,
//         );
//       }
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text('Edit Profile'),
//         leading: IconButton(
//           icon: Icon(Icons.arrow_back),
//           onPressed: () => Get.back(),
//         ),
//       ),
//       backgroundColor: Get.theme.scaffoldBackgroundColor,
//       body: SafeArea(
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 24),
//           child: Column(
//             children: [
//               Expanded(
//                 child: SingleChildScrollView(
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.center,
//                     children: [
//                       const SizedBox(height: 24),

//                       /// Circular image with "+" icon
//                       GestureDetector(
//                         onTap: () => settingsViewModel.pickImage(),
//                         child: Stack(
//                           children: [
//                             Obx(() {
//                               return CircularProfileImage(
//                                 imageFile: settingsViewModel.imageFile.value,
//                                 imageUrl:
//                                     settingsViewModel.imageFile.value == null
//                                         ? authController
//                                             .currentUser
//                                             .value
//                                             ?.profileImage
//                                         : null,
//                                 radius: 60,
//                               );
//                             }),
//                             Positioned(
//                               bottom: 5,
//                               right: 0,
//                               child: Container(
//                                 decoration: const BoxDecoration(
//                                   color: Pallete.primary,
//                                   shape: BoxShape.circle,
//                                 ),
//                                 padding: const EdgeInsets.all(4),
//                                 child: const Icon(
//                                   Icons.add,
//                                   size: 22,
//                                   color: Colors.white,
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),

//                       const SizedBox(height: 40),

//                       /// Name TextField
//                       QTextField(hintText: 'Name', controller: nameController),

//                       const SizedBox(height: 16),

//                       /// Phone Number TextField with always visible prefix
//                       Row(
//                         children: [
//                           Container(
//                             padding: const EdgeInsets.symmetric(
//                               horizontal: 12,
//                               vertical: 16,
//                             ),
//                             decoration: BoxDecoration(
//                               color: Get.theme.colorScheme.surface,
//                               borderRadius: BorderRadius.circular(12),
//                             ),
//                             child: Text(
//                               '+88',
//                               style: TextStyle(
//                                 color: Get.theme.colorScheme.onSurface,
//                               ),
//                             ),
//                           ),
//                           const SizedBox(width: 8),
//                           Expanded(
//                             child: QTextField(
//                               hintText: 'Phone Number (optional)',
//                               controller: phoneController,
//                             ),
//                           ),
//                         ],
//                       ),

//                       const SizedBox(height: 16),

//                       /// iOS-style context menu
//                       GestureDetector(
//                         onTap: () => _showCupertinoActionSheet(context),
//                         child: Container(
//                           padding: const EdgeInsets.symmetric(
//                             horizontal: 12,
//                             vertical: 18,
//                           ),
//                           decoration: BoxDecoration(
//                             color: Get.theme.colorScheme.surface,
//                             borderRadius: BorderRadius.circular(10),
//                           ),
//                           child: Row(
//                             children: [
//                               Expanded(
//                                 child: Obx(
//                                   () => Text(
//                                     settingsViewModel.selectedLevel.value,
//                                     style: TextStyle(
//                                       color: Get.theme.colorScheme.onSurface,
//                                     ),
//                                   ),
//                                 ),
//                               ),
//                               Icon(
//                                 CupertinoIcons.chevron_down,
//                                 color: Get.theme.colorScheme.onSurface,
//                                 size: 16,
//                               ),
//                             ],
//                           ),
//                         ),
//                       ),

//                       const SizedBox(height: 24),

//                       /// Update Button - replaced with QExpandedButton
//                       Obx(
//                         () => QExpandedButton(
//                           text: 'Update Profile',
//                           onPressed:
//                               settingsViewModel.isLoading.value
//                                   ? null
//                                   : _updateProfile,
//                           backgroundColor: Pallete.primary,
//                           textColor: Colors.white,
//                           isLoading: settingsViewModel.isLoading.value,
//                         ),
//                       ),

//                       Obx(
//                         () =>
//                             settingsViewModel.errorMessage.value.isNotEmpty
//                                 ? Padding(
//                                   padding: const EdgeInsets.only(top: 16),
//                                   child: Text(
//                                     settingsViewModel.errorMessage.value,
//                                     style: const TextStyle(color: Colors.red),
//                                     textAlign: TextAlign.center,
//                                   ),
//                                 )
//                                 : const SizedBox.shrink(),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   void _showCupertinoActionSheet(BuildContext context) {
//     showCupertinoLevelSelector(
//       context: context,
//       currentLevel: settingsViewModel.selectedLevel.value,
//       onLevelSelected: (item) {
//         settingsViewModel.setSelectedLevel(item);
//       },
//     );
//   }
// }
