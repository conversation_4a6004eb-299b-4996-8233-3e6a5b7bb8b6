// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/feature/settings/view/widgets/liked_question_card.dart';
// import 'package:qbank_bd/feature/settings/viewmodel/liked_questions_viewmodel.dart';

// class LikedQuestionsScreen extends StatelessWidget {
//   final viewModel = Get.put(LikedQuestionsViewModel());

//   LikedQuestionsScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text(
//           'Liked Questions',
//           style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
//         ),
//       ),
//       body: Obx(() {
//         if (viewModel.isLoading.value && viewModel.questions.isEmpty) {
//           return const Center(child: CircularProgressIndicator());
//         } else if (viewModel.questions.isEmpty && !viewModel.isLoading.value) {
//           return Center(
//             child: Column(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 Icon(Icons.favorite_border, size: 64, color: Colors.grey),
//                 SizedBox(height: 16),
//                 Text(
//                   "No liked questions yet",
//                   style: TextStyle(
//                     fontSize: 18,
//                     color: Get.theme.colorScheme.onSurface,
//                   ),
//                 ),
//                 SizedBox(height: 8),
//                 Text(
//                   "Questions you like will appear here",
//                   style: TextStyle(fontSize: 14, color: Colors.grey),
//                 ),
//               ],
//             ),
//           );
//         } else {
//           return ListView.builder(
//             controller: viewModel.scrollController,
//             padding: const EdgeInsets.all(14),
//             itemCount:
//                 viewModel.questions.length + (viewModel.hasMoreData ? 1 : 0),
//             itemBuilder: (context, index) {
//               if (index == viewModel.questions.length) {
//                 return Obx(() {
//                   return viewModel.isLoadingMore.value
//                       ? const Padding(
//                         padding: EdgeInsets.all(16.0),
//                         child: Center(child: CircularProgressIndicator()),
//                       )
//                       : const SizedBox();
//                 });
//               }

//               return LikedQuestionCard(question: viewModel.questions[index]);
//             },
//           );
//         }
//       }),
//     );
//   }
// }
