import 'package:flutter/material.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(title: Text('HTML Parser')),
        body: SingleChildScrollView(
          child: Html<PERSON>enderer(
            html: '''
<p class="text-base tracking-wide font-medium leading-relaxed">Hello <span class="katex"><annotation encoding="application/x-tex">x^2</annotation></span> World</p>
<img src="https://via.placeholder.com/150">
<table>
  <tr><td>Cell 1</td><td>Cell 2</td></tr>
  <tr><td>Cell 3</td><td>Cell 4</td></tr>
</table>
            ''',
          ),
        ),
      ),
    );
  }
}

// Represents an HTML node in the DOM tree
class HtmlNode {
  String tag;
  Map<String, String> attributes;
  List<dynamic> children; // Can contain HtmlNode or String

  HtmlNode(this.tag, this.attributes, this.children);
}

// Represents a math node for LaTeX (simplified for this example)
class MathNode {
  String latex;

  MathNode(this.latex);
}

// Widget to render HTML content
class HtmlRenderer extends StatelessWidget {
  final String html;

  HtmlRenderer({required this.html});

  @override
  Widget build(BuildContext context) {
    final nodes = parseHtml(html);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: nodes.map((node) => buildFlutterWidget(node)).toList(),
    );
  }

  // Set of HTML void tags that don't require closing tags
  static const Set<String> voidTags = {
    'img',
    'br',
    'hr',
    'meta',
    'link',
    'input',
    'area',
    'base',
    'col',
    'embed',
    'param',
    'source',
    'track',
    'wbr',
  };

  // Mapping of class names to TextStyle properties
  static final Map<String, TextStyle> classStyles = {
    'text-base': TextStyle(fontSize: 16),
    'tracking-wide': TextStyle(letterSpacing: 1.5),
    'font-medium': TextStyle(fontWeight: FontWeight.w500),
    'leading-relaxed': TextStyle(height: 1.5),
  };

  // Parse HTML string into a list of nodes
  List<dynamic> parseHtml(String html) {
    List<dynamic> nodes = [];
    List<HtmlNode> stack = [];
    int i = 0;

    while (i < html.length) {
      if (html[i] == '<') {
        int j = html.indexOf('>', i);
        if (j == -1) break;
        String tagContent = html.substring(i + 1, j).trim();

        if (tagContent.startsWith('/')) {
          // Closing tag
          String tagName = tagContent.substring(1);
          if (stack.isNotEmpty && stack.last.tag == tagName) {
            HtmlNode node = stack.removeLast();
            if (stack.isEmpty) {
              nodes.add(node);
            } else {
              stack.last.children.add(node);
            }
          }
        } else {
          // Start tag
          int spaceIndex = tagContent.indexOf(' ');
          String tagName =
              spaceIndex != -1
                  ? tagContent.substring(0, spaceIndex)
                  : tagContent;
          String attrString =
              spaceIndex != -1 ? tagContent.substring(spaceIndex + 1) : '';
          Map<String, String> attributes = parseAttributes(attrString);

          HtmlNode node = HtmlNode(tagName, attributes, []);

          // Special handling for <span class="katex">
          if (tagName == 'span' && attributes['class'] == 'katex') {
            int annotationStart = html.indexOf('<annotation', j);
            int annotationEnd = html.indexOf('</annotation>', j);
            if (annotationStart != -1 && annotationEnd != -1) {
              String annotation = html.substring(
                annotationStart,
                annotationEnd + 13,
              );
              RegExp regex = RegExp(r'application/x-tex">(.*?)</annotation');
              var match = regex.firstMatch(annotation);
              if (match != null) {
                node.children.add(MathNode(match.group(1)!));
                i = annotationEnd + 12;
              }
            }
          }

          if (voidTags.contains(tagName)) {
            if (stack.isEmpty) {
              nodes.add(node);
            } else {
              stack.last.children.add(node);
            }
          } else {
            stack.add(node);
          }
        }
        i = j + 1;
      } else {
        // Text content
        int nextTag = html.indexOf('<', i);
        if (nextTag == -1) nextTag = html.length;
        String text = html.substring(i, nextTag).trim();
        if (text.isNotEmpty) {
          if (stack.isEmpty) {
            nodes.add(text);
          } else {
            stack.last.children.add(text);
          }
        }
        i = nextTag;
      }
    }
    return nodes;
  }

  // Parse attributes from tag content
  Map<String, String> parseAttributes(String attrString) {
    Map<String, String> attributes = {};
    var regex = RegExp(
      r'(\w+)=("[^"]*"|\'
      '[^\']*)',
    );
    var matches = regex.allMatches(attrString);
    for (var match in matches) {
      String key = match.group(1)!;
      String value = match.group(2)!;
      attributes[key] = value.substring(1, value.length - 1);
    }
    return attributes;
  }

  // Build Flutter widget from node
  Widget buildFlutterWidget(dynamic node) {
    if (node is String) {
      return Text(node);
    }
    if (node is HtmlNode) {
      switch (node.tag) {
        case 'p':
          String classAttr = node.attributes['class'] ?? '';
          List<String> classes =
              classAttr.split(' ').where((c) => c.isNotEmpty).toList();
          TextStyle style = TextStyle();
          for (var className in classes) {
            if (classStyles.containsKey(className)) {
              style = style.merge(classStyles[className]!);
            }
          }
          List<InlineSpan> spans = [];
          for (var child in node.children) {
            if (child is String) {
              spans.add(TextSpan(text: child, style: style));
            } else if (child is HtmlNode) {
              spans.add(WidgetSpan(child: buildFlutterWidget(child)));
            } else if (child is MathNode) {
              spans.add(WidgetSpan(child: renderLatex(child.latex)));
            }
          }
          return RichText(text: TextSpan(children: spans));
        case 'img':
          String src = node.attributes['src'] ?? '';
          return Image.network(
            src,
            errorBuilder: (context, error, stackTrace) {
              return Text('Image failed to load');
            },
          );
        case 'table':
          List<TableRow> rows = [];
          for (var tr in node.children) {
            if (tr is HtmlNode && tr.tag == 'tr') {
              List<Widget> cells = [];
              for (var td in tr.children) {
                if (td is HtmlNode && td.tag == 'td') {
                  cells.add(
                    Padding(
                      padding: EdgeInsets.all(8),
                      child: buildFlutterWidget(
                        td.children.isNotEmpty ? td.children.first : '',
                      ),
                    ),
                  );
                }
              }
              rows.add(TableRow(children: cells));
            }
          }
          return Table(border: TableBorder.all(), children: rows);
        default:
          return Column(
            children:
                node.children
                    .map((child) => buildFlutterWidget(child))
                    .toList(),
          );
      }
    }
    if (node is MathNode) {
      return renderLatex(node.latex);
    }
    return SizedBox.shrink();
  }

  // Hypothetical function to render LaTeX (placeholder)
  Widget renderLatex(String latex) {
    return Text('[$latex]', style: TextStyle(fontStyle: FontStyle.italic));
  }
}
