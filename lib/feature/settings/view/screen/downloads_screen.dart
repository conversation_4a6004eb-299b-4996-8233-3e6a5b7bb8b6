// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/feature/qbanks/view/widgets/quiz_card.dart';
// import 'package:qbank_bd/feature/settings/viewmodel/downloads_viewmodel.dart';
// import 'package:qbank_bd/core/router/app_router.dart';
// import 'package:qbank_bd/feature/question/viewmodel/question_viewmodel.dart';

// class DownloadsScreen extends StatelessWidget {
//   final viewModel = Get.put(DownloadsViewModel());

//   DownloadsScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text(
//           'Downloaded Quizzes',
//           style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
//         ),
//       ),
//       body: Obx(() {
//         if (viewModel.isLoading.value && viewModel.downloadedQuizzes.isEmpty) {
//           return const Center(child: CircularProgressIndicator());
//         } else if (viewModel.downloadedQuizzes.isEmpty &&
//             !viewModel.isLoading.value) {
//           return Center(
//             child: Column(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 Icon(
//                   Icons.download_done_outlined,
//                   size: 64,
//                   color: Colors.grey,
//                 ),
//                 SizedBox(height: 16),
//                 Text(
//                   "No downloaded quizzes yet",
//                   style: TextStyle(
//                     fontSize: 18,
//                     color: Get.theme.colorScheme.onSurface,
//                   ),
//                 ),
//                 SizedBox(height: 8),
//                 Text(
//                   "Downloaded quizzes will appear here",
//                   style: TextStyle(fontSize: 14, color: Colors.grey),
//                 ),
//               ],
//             ),
//           );
//         } else {
//           return ListView.builder(
//             padding: const EdgeInsets.all(14),
//             itemCount: viewModel.downloadedQuizzes.length,
//             itemBuilder: (context, index) {
//               final quiz = viewModel.downloadedQuizzes[index];
//               return Padding(
//                 padding: const EdgeInsets.only(bottom: 14),
//                 child: GestureDetector(
//                   onTap: () {
//                     final questionViewModel = Get.put(QuestionViewModel());
//                     questionViewModel.fetchOfflineQuestions(quiz.id);
//                     Get.toNamed(
//                       AppRoutes.question,
//                       arguments: {'subjectId': quiz.subtopicId, 'quiz': quiz},
//                     );
//                   },
//                   child: QuizCard(
//                     quiz: quiz,
//                     isCompleted: false,
//                     isDownloaded: true,
//                     onDownload: () {
//                       viewModel.deleteDownloadedQuiz(quiz.id);
//                     },
//                   ),
//                 ),
//               );
//             },
//           );
//         }
//       }),
//     );
//   }
// }
