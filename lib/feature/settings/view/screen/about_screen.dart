// import 'dart:convert';
// 
// import 'package:flutter/material.dart';
// import 'package:flutter_html/flutter_html.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/feature/question/viewmodel/question_viewmodel.dart';
// 
// class AboutScreen extends StatelessWidget {
//   AboutScreen({super.key});
//   final QuestionViewModel viewmodel = Get.find();
// 
//   @override
//   Widget build(BuildContext context) {
//     final any = viewmodel.questions[0].questionText;
//     return SafeArea(
//       child: SingleChildScrollView(
//         child: Html(
//           data: cleanHtml(any), // Try trimming first
//         ),
//       ),
//     );
//   }
// 
//   String cleanHtml(String raw) {
//     // Step 1: Try JSON decode if string is double-escaped
//     try {
//       raw = jsonDecode(raw);
//     } catch (_) {
//       // Not a JSON string, move on
//     }
// 
//     // Step 2: Remove escaped characters manually
//     return raw
//         .replaceAll(r'\"', '"')
//         .replaceAll(r"\'", "'")
//         .replaceAll(r'\\', '\\') // fix double backslashes
//         .replaceAll(r'\/', '/')
//         .replaceAll(RegExp(r'^"|"$'), '') // remove outer quotes
//         .trim();
//   }
// }
