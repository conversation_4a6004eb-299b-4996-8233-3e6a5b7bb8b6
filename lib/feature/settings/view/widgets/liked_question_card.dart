// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/core/common/model/question_model.dart';
// import 'package:qbank_bd/feature/settings/viewmodel/liked_questions_viewmodel.dart';
// import '../../../question/view/widgets/explanation_section.dart';

// class LikedQuestionCard extends StatelessWidget {
//   final QuestionModel question;
//   final VoidCallback? onUnlike;
//   final bool showLikeButton;

//   final LikedQuestionsViewModel viewModel = Get.put(LikedQuestionsViewModel());

//   LikedQuestionCard({
//     super.key,
//     required this.question,
//     this.onUnlike,
//     this.showLikeButton = true,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Card(
//       elevation: 0,
//       color: Get.theme.cardColor.withAlpha(100),
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
//       margin: const EdgeInsets.symmetric(vertical: 8),
//       child: Padding(
//         padding: const EdgeInsets.all(14),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Expanded(
//                   child: Text(
//                     question.questionText,
//                     style: TextStyle(fontSize: 18),
//                   ),
//                 ),
//                 if (showLikeButton)
//                   IconButton(
//                     icon: Icon(
//                       CupertinoIcons.heart_fill,
//                       size: 20,
//                       color: Colors.red,
//                     ),
//                     tooltip: 'Unlike',
//                     padding: EdgeInsets.zero,
//                     constraints: const BoxConstraints(),
//                     visualDensity: VisualDensity.compact,
//                     onPressed: () => viewModel.handleUnlike(question.id),
//                   ),
//               ],
//             ),
//             if (question.options != null)
//               ...question.options!.asMap().entries.map(
//                 (entry) => _buildOptionTile(entry.key, entry.value),
//               ),
//             const SizedBox(height: 16),
//             ExplanationSection(explanation: question.explanation),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildOptionTile(int optionIndex, String optionText) {
//     // Assuming first option (index 0) is always correct
//     final isCorrect = optionIndex == 0;

//     // Get the option letter (A, B, C, D, E)
//     final optionLetter = String.fromCharCode(
//       65 + optionIndex,
//     ); // ASCII 'A' is 65

//     return Container(
//       margin: const EdgeInsets.symmetric(vertical: 6),
//       padding: const EdgeInsets.all(12),
//       decoration: BoxDecoration(
//         color: Get.theme.colorScheme.onSurface.withAlpha(16),
//         borderRadius: BorderRadius.circular(8),
//       ),
//       child: Row(
//         children: [
//           // Option letter circle
//           Container(
//             width: 24,
//             height: 24,
//             margin: const EdgeInsets.only(right: 12),
//             decoration: BoxDecoration(
//               color: isCorrect ? Colors.green : Colors.transparent,
//               shape: BoxShape.circle,
//               border: Border.all(
//                 color: Get.theme.colorScheme.onPrimary.withAlpha(70),
//                 width: 0.5,
//               ),
//             ),
//             child: Center(
//               child: Text(
//                 optionLetter,
//                 style: TextStyle(
//                   color:
//                       isCorrect
//                           ? Colors.white
//                           : Get.theme.colorScheme.onPrimary,
//                 ),
//               ),
//             ),
//           ),
//           Expanded(child: Text(optionText)),
//         ],
//       ),
//     );
//   }
// }
