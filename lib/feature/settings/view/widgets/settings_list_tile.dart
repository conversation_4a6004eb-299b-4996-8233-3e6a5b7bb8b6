import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SettingsListTile extends StatelessWidget {
  final IconData icon;
  final String title;
  final Color? iconColor;
  final Function onTap;

  const SettingsListTile({
    super.key,
    required this.icon,
    required this.title,
    this.iconColor,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color:
            iconColor != null
                ? iconColor!.withAlpha(40)
                : Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        //border: Border.all(color: Colors.grey.withAlpha(40), width: 1),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: iconColor ?? Get.theme.colorScheme.primary,
          size: 20,
        ),
        title: Text(
          title,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
        ),
        trailing: const Icon(CupertinoIcons.right_chevron),
        onTap: () => onTap(),
      ),
    );
  }
}
