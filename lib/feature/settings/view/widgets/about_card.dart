import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qbank_bd/core/common/model/card_model.dart';

class AboutCard extends StatelessWidget {
  final CardModel card;

  const AboutCard({super.key, required this.card});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header section
        Text(
          card.title,
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        // Body text
        Text(
          card.bodyText,
          style: TextStyle(
            fontSize: 16,
            color: Get.theme.colorScheme.onPrimary.withAlpha(100),
          ),
        ),
      ],
    );
  }
}
