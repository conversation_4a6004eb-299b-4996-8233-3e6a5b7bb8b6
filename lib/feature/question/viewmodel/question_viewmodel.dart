// import 'package:flutter/cupertino.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/core/common/model/question_model.dart';
// import 'package:qbank_bd/core/services/database_helper.dart';
// import 'package:qbank_bd/core/utils/utils.dart';
// import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';
// import 'package:qbank_bd/feature/question/repository/question_repository.dart';

// class QuestionViewModel extends GetxController {
//   final questions = <QuestionModel>[].obs;
//   final isLoading = false.obs;
//   final isSubmitting = false.obs;
//   final _repository = QuestionRepository();

//   // Make user nullable and initialize it lazily
//   Rx<dynamic>? _user;
//   dynamic get user {
//     if (_user == null) {
//       try {
//         _user = Get.find<AuthController>().currentUser;
//         return _user?.value;
//       } catch (e) {
//         print('Error getting user: $e');
//         return null;
//       }
//     }
//     return _user?.value;
//   }

//   final selectedOptions = <int?>[].obs;
//   final correctAnswers = <bool>[].obs;
//   final score = 0.obs;
//   final scrollController = ScrollController();
//   String? currentQuizId;
//   final likedQuestions = <String>[].obs;

//   // Add these properties to track time
//   final RxString elapsedTime = "00:00".obs;
//   DateTime? _quizStartTime;

//   // Add this property to track reported questions
//   final reportedQuestions = <String>[].obs;

//   @override
//   void onInit() {
//     super.onInit();
//     fetchQuestions("hsc_bangla_first_paper_mcq_dhaka_2020_copy27");
//     selectedOptions.value = [];
//     fetchLikedQuestions();
//   }

//   void selectOption(int questionIndex, int optionIndex) {
//     selectedOptions[questionIndex] = optionIndex;
//     selectedOptions.refresh();
//   }

//   void scrollToTop() {
//     scrollController.animateTo(
//       0,
//       duration: const Duration(milliseconds: 500),
//       curve: Curves.easeInOut,
//     );
//   }

//   bool isOptionSelected(int questionIndex, int optionIndex) =>
//       selectedOptions.length > questionIndex &&
//       selectedOptions[questionIndex] == optionIndex;

//   Future<void> fetchQuestions(String quizId) async {
//     print(quizId);
//     try {
//       currentQuizId = quizId;
//       isLoading.value = true;
//       final result = await _repository.fetchQuestionsByquizId(quizId);
//       questions.assignAll(result);
//       selectedOptions.value = List.filled(result.length, null);
//     } catch (e) {
//       print('Error fetching subjects: $e');
//       questions.clear();
//       selectedOptions.clear();
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   void checkAnswers() {
//     correctAnswers.clear();
//     int correctCount = 0;

//     for (int i = 0; i < questions.length; i++) {
//       final isCorrect =
//           selectedOptions[i] == 0; // Assuming first option is correct
//       correctAnswers.add(isCorrect);
//       if (isCorrect) correctCount++;
//     }

//     score.value = correctCount;
//   }

//   int getCorrectOptionIndex(int questionIndex) =>
//       0; // Assuming first option is correct

//   Future<void> submitQuiz(String subjectId) async {
//     if (currentQuizId == null) {
//       print('Error: No quiz ID found');
//       return;
//     }

//     try {
//       isSubmitting.value = true;
//       await _repository.submitQuiz(
//         userId: user.id,
//         subjectId: subjectId,
//         quizId: currentQuizId!,
//         scoreValue: score.value,
//       );
//     } catch (e) {
//       print('Error submitting quiz: $e');
//       rethrow;
//     } finally {
//       isSubmitting.value = false;
//     }
//   }

//   Future<void> fetchLikedQuestions() async {
//     try {
//       final response = await _repository.getUserLikedQuestions(user.id);
//       likedQuestions.assignAll(response);
//     } catch (e) {
//       print('Error fetching liked questions: $e');
//     }
//   }

//   bool isQuestionLiked(int questionId) {
//     return likedQuestions.contains(questionId.toString());
//   }

//   Future<void> toggleLikeQuestion(int questionId) async {
//     final questionIdStr = questionId.toString();
//     try {
//       if (isQuestionLiked(questionId)) {
//         // Remove from liked questions
//         likedQuestions.remove(questionIdStr);
//         await _repository.updateLikedQuestions(user.id, likedQuestions);
//       } else {
//         // Add to liked questions
//         if (likedQuestions.length >= 100) {
//           // Remove the oldest item (first in the list)
//           likedQuestions.removeAt(0);
//         }
//         likedQuestions.add(questionIdStr);
//         await _repository.updateLikedQuestions(user.id, likedQuestions);
//       }
//     } catch (e) {
//       print('Error toggling like: $e');
//       showCustomSnackbar(
//         title: 'Error',
//         message: 'Couldn\'t like or unlike the question. Please try again.',
//         isSuccess: false,
//       );
//     }
//   }

//   // Add this method to start tracking time
//   void startQuizTimer(int durationMinutes) {
//     _quizStartTime = DateTime.now();
//   }

//   // Add this method to calculate elapsed time
//   void calculateElapsedTime() {
//     if (_quizStartTime == null) {
//       elapsedTime.value = "00:00";
//       return;
//     }

//     final now = DateTime.now();
//     final difference = now.difference(_quizStartTime!);

//     // Calculate minutes and seconds
//     final minutes = difference.inMinutes;
//     final seconds = difference.inSeconds % 60;

//     // Format as MM:SS
//     elapsedTime.value =
//         "${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";
//   }

//   // Add this method to fetch reported questions
//   Future<void> _fetchReportedQuestions() async {
//     try {
//       final response = await _repository.getUserReportedQuestions(user.id);
//       reportedQuestions.assignAll(response);
//     } catch (e) {
//       print('Error fetching reported questions: $e');
//     }
//   }

//   // Check if question is already reported
//   bool isQuestionReported(int questionId) {
//     return reportedQuestions.contains(questionId.toString());
//   }

//   // Modified method to handle question reports
//   Future<void> reportQuestion(
//     int questionId,
//     String reason,
//     String comment,
//   ) async {
//     await _fetchReportedQuestions();
//     final questionIdStr = questionId.toString();

//     // Check if already reported
//     if (isQuestionReported(questionId)) {
//       showCustomSnackbar(
//         title: 'Already Reported',
//         message: 'You have already reported this question.',
//         isSuccess: true,
//       );
//       return;
//     }

//     try {
//       // Add to reported questions list
//       reportedQuestions.add(questionIdStr);

//       // Update user_data with new reported question
//       await _repository.updateReportedQuestions(user.id, reportedQuestions);

//       // Submit the report to reports table
//       await _repository.submitQuestionReport(
//         userId: user.id,
//         questionId: questionId,
//         reason: reason,
//         comment: comment,
//       );

//       showCustomSnackbar(
//         title: 'Report Submitted',
//         message:
//             'Thank you for reporting this question. We will review it shortly.',
//         isSuccess: true,
//       );
//     } catch (e) {
//       // Remove from local list if failed
//       reportedQuestions.remove(questionIdStr);

//       print('Error reporting question: $e');
//       showCustomSnackbar(
//         title: 'Error',
//         message: 'Couldn\'t submit your report. Please try again.',
//         isSuccess: false,
//       );
//     }
//   }

//   Future<void> fetchOfflineQuestions(String quizId) async {
//     try {
//       currentQuizId = quizId;
//       isLoading.value = true;

//       // Get database instance
//       final db = await DatabaseHelper.instance.database;

//       // Fetch questions from local storage
//       final result = await db.query(
//         'questions',
//         where: 'quizId LIKE ?',
//         whereArgs: ['%$quizId%'],
//       );

//       if (result.isEmpty) {
//         showCustomSnackbar(
//           title: 'No Questions',
//           message:
//               'No questions found for this quiz. Try downloading it again.',
//           isSuccess: false,
//         );
//       }

//       // Convert to QuestionModel objects
//       questions.value =
//           result.map((map) => QuestionModel.fromMap(map)).toList();
//       selectedOptions.value = List.filled(result.length, null);
//     } catch (e) {
//       showCustomSnackbar(
//         title: 'Error',
//         message: 'Failed to load questions: $e',
//         isSuccess: false,
//       );
//     } finally {
//       isLoading.value = false;
//     }
//   }
// }
