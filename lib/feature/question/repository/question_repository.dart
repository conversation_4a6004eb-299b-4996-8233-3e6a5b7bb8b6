// import 'package:get/get.dart';
// import 'package:qbank_bd/core/common/model/question_model.dart';
// import 'package:qbank_bd/core/utils/utils.dart';
// import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';
// import 'package:qbank_bd/feature/qbanks/viewmodel/quiz_viewmodel.dart';
// import 'package:supabase_flutter/supabase_flutter.dart';

// class QuestionRepository {
//   static final SupabaseClient _client = Supabase.instance.client;

//   Future<List<QuestionModel>> fetchQuestionsByquizId(String quizId) async {
//     try {
//       final response = await _client
//           .from('questions')
//           .select()
//           .contains('quizId', [quizId])
//           .order('id', ascending: true);

//       return (response as List)
//           .map((item) => QuestionModel.fromMap(item as Map<String, dynamic>))
//           .toList();
//     } catch (e) {
//       showCustomSnackbar(
//         title: 'Error',
//         message: "Error fetching questions. Please try again.",
//         isSuccess: false,
//       );
//       rethrow;
//     }
//   }

//   Future<void> submitQuiz({
//     required String userId,
//     required String subjectId,
//     required String quizId,
//     required int scoreValue,
//   }) async {
//     try {
//       final currentUser = Get.find<AuthController>().currentUser.value!;
//       final quizViewModel = Get.find<QuizViewModel>();

//       // Get quizzes from quizViewModel
//       List<String> quizzes = List<String>.from(quizViewModel.userQuizzes);
//       List<String> courses = List<String>.from(currentUser.courses);
//       int currentPoints = currentUser.points;

//       // Calculate streak
//       int streak = currentUser.streak;
//       DateTime now = DateTime.now();
//       DateTime today = DateTime(now.year, now.month, now.day);

//       // Check if this is a new day compared to last quiz date
//       if (currentUser.lastQuizDate != null) {
//         DateTime lastQuizDay = DateTime(
//           currentUser.lastQuizDate!.year,
//           currentUser.lastQuizDate!.month,
//           currentUser.lastQuizDate!.day,
//         );

//         // Calculate days difference
//         int daysDifference = today.difference(lastQuizDay).inDays;

//         if (daysDifference == 1) {
//           streak += 1;
//         } else if (daysDifference > 1) {
//           streak = 1;
//         }
//       } else {
//         streak = 1;
//       }

//       // Add subject to courses if not exists
//       if (!courses.contains(subjectId)) {
//         courses.add(subjectId);
//       }

//       var exams = currentUser.exams;
//       // Add quiz to quizzes if not exists
//       if (!quizzes.contains(quizId)) {
//         quizzes.add(quizId);
//         exams += 1;
//       }

//       // Update users table with new courses, points, and streak info
//       await _client
//           .from('users')
//           .update({
//             'courses': courses,
//             'points': currentPoints + scoreValue,
//             'streak': streak,
//             'lastQuizDate': now.toIso8601String(),
//             'exams': exams,
//           })
//           .eq('id', userId);

//       // Update user_data table with new quizzes
//       await _client
//           .from('user_data')
//           .upsert({'id': userId, 'quizzes': quizzes})
//           .eq('id', userId);
//     } catch (e) {
//       showCustomSnackbar(
//         title: 'Error',
//         message: 'Failed to submit quiz: $e',
//         isSuccess: false,
//       );
//       rethrow;
//     }
//   }

//   Future<List<String>> getUserLikedQuestions(String userId) async {
//     try {
//       final response =
//           await _client
//               .from('user_data')
//               .select('liked_questions')
//               .eq('id', userId)
//               .single();

//       return response['liked_questions'] != null
//           ? List<String>.from(response['liked_questions'])
//           : [];
//     } catch (e) {
//       showCustomSnackbar(
//         title: 'Error',
//         message: 'Failed to fetch liked questions: $e',
//         isSuccess: false,
//       );
//       return [];
//     }
//   }

//   Future<void> updateLikedQuestions(
//     String userId,
//     List<String> likedQuestions,
//   ) async {
//     try {
//       await _client
//           .from('user_data')
//           .update({'liked_questions': likedQuestions})
//           .eq('id', userId);
//     } catch (e) {
//       showCustomSnackbar(
//         title: 'Error',
//         message: 'Failed to update liked questions: $e',
//         isSuccess: false,
//       );
//       rethrow;
//     }
//   }

//   Future<List<String>> getUserReportedQuestions(String userId) async {
//     try {
//       final response =
//           await _client
//               .from('user_data')
//               .select('reported_questions')
//               .eq('id', userId)
//               .single();

//       return response['reported_questions'] != null
//           ? List<String>.from(response['reported_questions'])
//           : [];
//     } catch (e) {
//       print('Error fetching reported questions: $e');
//       showCustomSnackbar(
//         title: 'Error',
//         message: 'Failed to fetch reported questions: $e',
//         isSuccess: false,
//       );
//       return [];
//     }
//   }

//   Future<void> updateReportedQuestions(
//     String userId,
//     List<String> reportedQuestions,
//   ) async {
//     try {
//       await _client
//           .from('user_data')
//           .update({'reported_questions': reportedQuestions})
//           .eq('id', userId);
//     } catch (e) {
//       print('Error updating reported questions: $e');
//       showCustomSnackbar(
//         title: 'Error',
//         message: 'Failed to update reported questions: $e',
//         isSuccess: false,
//       );
//       rethrow;
//     }
//   }

//   Future<void> submitQuestionReport({
//     required String userId,
//     required int questionId,
//     required String reason,
//     required String comment,
//   }) async {
//     try {
//       await _client.from('reports').insert({
//         'id': userId + questionId.toString(),
//         'userId': userId,
//         'questionId': questionId,
//         'reason': reason,
//         'comment': comment,
//         'createdAt': DateTime.now().toIso8601String(),
//         'status': false,
//       });
//     } catch (e) {
//       print('Error submitting question report: $e');
//       showCustomSnackbar(
//         title: 'Error',
//         message: 'Failed to submit report: $e',
//         isSuccess: false,
//       );
//       rethrow;
//     }
//   }
// }
