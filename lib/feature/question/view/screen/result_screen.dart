// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/core/common/widgets/q_buttons.dart';
// import 'package:qbank_bd/core/utils/pallete.dart';
// import 'package:qbank_bd/feature/question/view/widgets/modified_question_card.dart'
//     as card;
// import 'package:qbank_bd/feature/question/view/widgets/question_card.dart';
// import 'package:qbank_bd/feature/question/view/widgets/score_summary_section.dart';
// import 'package:qbank_bd/feature/question/viewmodel/question_viewmodel.dart';

// class ResultScreen extends StatelessWidget {
//   final QuestionViewModel viewModel = Get.find();
//   final ScrollController _scrollController = ScrollController();
//   final bool isOfflineMode;

//   ResultScreen({super.key})
//     : isOfflineMode = Get.arguments?['isOfflineMode'] ?? false;

//   @override
//   Widget build(BuildContext context) {
//     return PopScope(
//       canPop: false,
//       child: Scaffold(
//         appBar: AppBar(
//           title: const Text(
//             'Bangla 1st',
//             style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
//           ),
//           leading: IconButton(
//             icon: const Icon(Icons.close),
//             onPressed: () {
//               Get.back();
//               Get.back();
//             },
//           ),
//           actions: [
//             Padding(
//               padding: const EdgeInsets.only(right: 8),
//               child: QTextButton(
//                 text: 'Done',
//                 textColor: Colors.white,
//                 backgroundColor: Pallete.primary,
//                 onPressed: () {
//                   Get.back();
//                   Get.back();
//                 },
//               ),
//             ),
//           ],
//         ),
//         body: ListView.builder(
//           controller: _scrollController,
//           padding: const EdgeInsets.all(14),
//           itemCount: viewModel.questions.length + 1,
//           itemBuilder:
//               (context, index) =>
//                   index == 0
//                       ? ScoreSummarySection(
//                         timerString: viewModel.elapsedTime.value,
//                         totalQuestions: viewModel.questions.length,
//                         answeredQuestions:
//                             viewModel.selectedOptions
//                                 .where((option) => option != null)
//                                 .length,
//                         correctAnswers: viewModel.score.value,
//                         score:
//                             (viewModel.score.value /
//                                 viewModel.questions.length) *
//                             100,
//                       )
//                       : QuestionCard(
//                         questionIndex: index - 1,
//                         question: viewModel.questions[index - 1],
//                         isExplanation: true,
//                         isOfflineMode: isOfflineMode,
//                       ),
//         ),
//       ),
//     );
//   }
// }
