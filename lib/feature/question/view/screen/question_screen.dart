// import 'dart:async';
// import 'package:flutter/material.dart';
// import 'package:font_awesome_flutter/font_awesome_flutter.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/core/common/model/quiz_model.dart';
// import 'package:qbank_bd/core/common/widgets/q_buttons.dart';
// import 'package:qbank_bd/core/router/app_router.dart';
// import 'package:qbank_bd/core/utils/pallete.dart';
// import 'package:qbank_bd/core/utils/utils.dart';
// import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';
// import 'package:qbank_bd/feature/qbanks/viewmodel/quiz_viewmodel.dart';
// import 'package:qbank_bd/feature/question/view/widgets/question_card.dart';
// import 'package:qbank_bd/feature/question/viewmodel/question_viewmodel.dart';

// class QuestionScreen extends StatefulWidget {
//   final String subjectId;
//   final QuizModel quiz;

//   const QuestionScreen({
//     super.key,
//     required this.subjectId,
//     required this.quiz,
//   });

//   @override
//   State<QuestionScreen> createState() => _QuestionScreenState();
// }

// class _QuestionScreenState extends State<QuestionScreen> {
//   final QuestionViewModel viewModel = Get.find();
//   //final QuizViewModel _quizViewModel = Get.find();
//   late final QuizViewModel? _quizViewModel;
//   final ScrollController _scrollController = ScrollController();

//   Timer? _timer;
//   final RxInt _remainingSeconds = 0.obs;

//   final bool isOfflineMode;

//   _QuestionScreenState()
//     : isOfflineMode = Get.arguments['quiz'].downloadedAt != null {
//     _quizViewModel = isOfflineMode ? null : Get.find<QuizViewModel>();
//   }

//   @override
//   void initState() {
//     super.initState();
//     // Check if the quiz is downloaded and use the appropriate fetch method
//     if (isOfflineMode) {
//       viewModel.fetchOfflineQuestions(widget.quiz.id);
//     } else {
//       viewModel.fetchQuestions(widget.quiz.id);
//     }
//     _startTimer(widget.quiz.duration);
//     viewModel.startQuizTimer(widget.quiz.duration);
//   }

//   @override
//   void dispose() {
//     _timer?.cancel();
//     super.dispose();
//   }

//   void _startTimer(int durationMinutes) {
//     // Convert minutes to seconds
//     _remainingSeconds.value = durationMinutes * 60;

//     _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
//       if (_remainingSeconds.value > 0) {
//         _remainingSeconds.value--;
//       } else {
//         _timer?.cancel();
//         // Auto-submit when time is up
//         _submitQuiz();
//       }
//     });
//   }

//   String get _formattedTime {
//     final minutes = (_remainingSeconds.value / 60).floor();
//     final seconds = _remainingSeconds.value % 60;
//     return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
//   }

//   Future<void> _submitQuiz() async {
//     // Cancel the timer immediately to prevent it from triggering again
//     _timer?.cancel();

//     // Count answered questions
//     final answeredCount =
//         viewModel.selectedOptions.where((option) => option != null).length;
//     final totalCount = viewModel.questions.length;

//     // If not all questions are answered, show confirmation dialog
//     if (answeredCount < totalCount) {
//       showAlertDialog(
//         'Incomplete Quiz',
//         'You have answered $answeredCount questions out of $totalCount questions. Are you sure you want to submit?',
//         () {
//           // Cancel submission - restart the timer if user cancels
//           Get.back();
//           _startTimer(_remainingSeconds.value ~/ 60);
//         },
//         () async {
//           // Confirm submission
//           Get.back();
//           await _processSubmission();
//         },
//       );
//     } else {
//       // All questions answered, proceed directly
//       await _processSubmission();
//     }
//   }

//   Future<void> _processSubmission() async {
//     try {
//       // Show loading indicator
//       Get.dialog(
//         const Center(child: CircularProgressIndicator()),
//         barrierDismissible: false,
//       );

//       // Calculate elapsed time before checking answers
//       viewModel.calculateElapsedTime();
//       viewModel.checkAnswers();

//       // Check if user is actually offline, not just using a downloaded quiz
//       final authController = Get.find<AuthController>();
//       final bool isActuallyOffline = !authController.isConnected.value;

//       // If truly offline, skip submission and just go to results
//       if (isActuallyOffline) {
//         Get.toNamed(AppRoutes.result, arguments: {'isOfflineMode': true});
//         return;
//       }

//       // Online mode - submit quiz and update user data
//       final currentQuizId = widget.quiz.id;
//       await viewModel.submitQuiz(widget.subjectId);

//       // Only fetch user quizzes if this quiz wasn't already in the list
//       if (_quizViewModel != null &&
//           !_quizViewModel.userQuizzes.contains(currentQuizId)) {
//         await _quizViewModel.fetchUsersQuizzes();
//       }

//       // Close loading dialog before navigation
//       Get.back();
//       Get.toNamed(AppRoutes.result, arguments: {'isOfflineMode': false});
//     } catch (e) {
//       // Close loading dialog if there's an error
//       if (Get.isDialogOpen ?? false) Get.back();
//       debugPrint('Error submitting quiz: $e');
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         leadingWidth: 80,
//         leading: TextButton(
//           onPressed:
//               () => showAlertDialog(
//                 'Cancel the Exam',
//                 'Are you sure you want to cancel the exam?',
//                 () {
//                   Get.back();
//                 },
//                 () {
//                   Get.back();
//                   Get.back();
//                 },
//               ),
//           child: const Text(
//             'Cancel',
//             style: TextStyle(color: Colors.red, fontSize: 16),
//           ),
//         ),
//         centerTitle: true,
//         title: Obx(
//           () => Container(
//             padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
//             decoration: BoxDecoration(
//               color:
//                   _remainingSeconds.value < 60
//                       ? Colors.red.withAlpha(20)
//                       : Colors.green.withAlpha(20),
//               borderRadius: BorderRadius.circular(8),
//             ),
//             child: Row(
//               mainAxisSize: MainAxisSize.min,
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 Icon(
//                   FontAwesomeIcons.clock,
//                   color:
//                       _remainingSeconds.value < 60 ? Colors.red : Colors.green,
//                   size: 16,
//                 ),
//                 const SizedBox(width: 6),
//                 Text(
//                   _formattedTime,
//                   style: TextStyle(
//                     fontSize: 16,
//                     fontWeight: FontWeight.bold,
//                     color:
//                         _remainingSeconds.value < 60
//                             ? Colors.red
//                             : Colors.green,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ),
//         actions: [
//           Padding(
//             padding: const EdgeInsets.only(right: 8),
//             child: Obx(
//               () => QTextButton(
//                 text: 'Submit',
//                 onPressed: viewModel.questions.isEmpty ? null : _submitQuiz,
//                 backgroundColor: Pallete.primary,
//                 textColor: Colors.white,
//               ),
//             ),
//           ),
//         ],
//         bottom: PreferredSize(
//           preferredSize: const Size.fromHeight(1),
//           child: Divider(
//             height: 1,
//             thickness: 1,
//             color: Colors.grey.withAlpha(40),
//           ),
//         ),
//       ),
//       body: Obx(() {
//         if (viewModel.isLoading.value) {
//           return const Center(child: CircularProgressIndicator());
//         } else if (viewModel.questions.isEmpty) {
//           return Center(
//             child: Text(
//               "No questions available.",
//               style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
//             ),
//           );
//         } else {
//           return ListView.builder(
//             controller: _scrollController,
//             padding: const EdgeInsets.all(14),
//             itemCount: viewModel.questions.length,
//             itemBuilder: (context, index) {
//               if (index < 0 || index >= viewModel.questions.length) {
//                 return const SizedBox();
//               }

//               final question = viewModel.questions[index];
//               return QuestionCard(
//                 questionIndex: index,
//                 question: question,
//                 isOfflineMode: isOfflineMode,
//               );
//             },
//           );
//         }
//       }),
//       floatingActionButton: Obx(
//         () =>
//             viewModel.questions.isEmpty
//                 ? const SizedBox()
//                 : FloatingActionButton(
//                   mini: true,
//                   elevation: 0,
//                   backgroundColor: Get.theme.colorScheme.onPrimary.withAlpha(
//                     30,
//                   ),
//                   foregroundColor: Get.theme.colorScheme.onPrimary,
//                   shape: const CircleBorder(),
//                   onPressed: () {
//                     _scrollController.animateTo(
//                       0,
//                       duration: const Duration(milliseconds: 500),
//                       curve: Curves.easeInOut,
//                     );
//                   },
//                   child: Icon(
//                     FontAwesomeIcons.arrowUp,
//                     size: 20,
//                     color: Get.theme.colorScheme.onPrimary.withAlpha(140),
//                   ),
//                 ),
//       ),
//     );
//   }
// }
