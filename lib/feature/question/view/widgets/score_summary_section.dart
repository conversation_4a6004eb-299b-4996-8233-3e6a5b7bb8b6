import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qbank_bd/core/utils/pallete.dart';
import 'package:qbank_bd/feature/question/view/widgets/scorecard.dart';

class ScoreSummarySection extends StatelessWidget {
  final String timerString;
  final int totalQuestions;
  final int answeredQuestions;
  final int correctAnswers;
  final double score; // assuming 0 to 100

  const ScoreSummarySection({
    super.key,
    required this.timerString,
    required this.totalQuestions,
    required this.answeredQuestions,
    required this.correctAnswers,
    required this.score,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      color: Get.theme.cardColor.withAlpha(80),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Time: $timerString",
              style: TextStyle(
                fontSize: 18,
                color: Get.theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            // Wrap the Row in a Container with padding to ensure it doesn't overflow
            SizedBox(
              width: double.infinity,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ScoreCard(title: "Total", value: totalQuestions),
                  ScoreCard(title: "Answered", value: answeredQuestions),
                  ScoreCard(title: "Correct", value: correctAnswers),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Pallete.primary.withAlpha(20),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                "Score: ${score.toInt()}%",
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Pallete.primary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
