// import 'package:flutter/material.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter_html/flutter_html.dart';
// import 'package:flutter_html_table/flutter_html_table.dart';
// import 'package:flutter_math_fork/flutter_math.dart';
// import 'package:font_awesome_flutter/font_awesome_flutter.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/core/common/model/question_model.dart';
// import 'package:qbank_bd/core/utils/pallete.dart';
// import 'package:qbank_bd/feature/question/view/widgets/report_bottom_sheet.dart';
// import 'package:qbank_bd/feature/question/viewmodel/question_viewmodel.dart';
// import 'option_tile.dart';
// import 'explanation_section.dart';

// class QuestionCard extends StatelessWidget {
//   final int questionIndex;
//   final QuestionModel question;
//   final bool isExplanation;
//   final bool isOfflineMode;

//   final QuestionViewModel questionVm = Get.find<QuestionViewModel>();

//   QuestionCard({
//     super.key,
//     required this.questionIndex,
//     required this.question,
//     this.isExplanation = false,
//     this.isOfflineMode = false,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Card(
//       elevation: 0,
//       color: Get.theme.cardColor.withAlpha(100), // Lighter background
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
//       margin: const EdgeInsets.symmetric(vertical: 8),
//       child: Container(
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(14),
//           //border: Border.all(color: Colors.grey.withAlpha(40), width: 1),
//         ),
//         child: Padding(
//           padding: const EdgeInsets.all(14),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Row(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Expanded(
//                     // Add this to constrain the Html widget
//                     child: Html(
//                       data: cleanHtml(question.questionText),
//                       shrinkWrap: true,
//                       style: {
//                         "body": Style(
//                           color: Get.theme.colorScheme.onPrimary,
//                           fontSize: FontSize(16),
//                         ),
//                         // Add styles for table elements here
//                         "table": Style(
//                           border: Border.all(
//                             color: Colors.white,
//                             width: 1,
//                           ), // Optional: border around the whole table
//                           //borderCollapse: BorderCollapse.collapse, // This helps in merging borders
//                         ),
//                         "th": Style(
//                           border: Border.all(color: Colors.white, width: 1),
//                           padding: HtmlPaddings.all(
//                             6.0,
//                           ), // Add some padding for better appearance
//                           backgroundColor:
//                               Colors
//                                   .grey[200], // Optional: background for header cells
//                         ),
//                         "td": Style(
//                           border: Border.all(color: Colors.white, width: 1),
//                           padding: HtmlPaddings.all(
//                             6.0,
//                           ), // Add some padding for better appearance
//                         ),
//                       },
//                       extensions: [
//                         TagExtension(
//                           tagsToExtend: {"tex"},
//                           builder: (extensionContext) {
//                             return Math.tex(
//                               extensionContext.innerHtml,
//                               textStyle: extensionContext.styledElement?.style
//                                   .generateTextStyle()
//                                   .copyWith(
//                                     color: Get.theme.colorScheme.onPrimary,
//                                   ),
//                               onErrorFallback: (FlutterMathException e) {
//                                 return Text(
//                                   'Error rendering math: ${e.message}',
//                                 );
//                               },
//                             );
//                           },
//                         ),
//                         TableHtmlExtension(),
//                       ],
//                     ),
//                   ),
//                   if (!isOfflineMode)
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.end,
//                       mainAxisSize: MainAxisSize.min,
//                       children:
//                           [
//                                 Obx(
//                                   () => _buildIconButton(
//                                     questionVm.isQuestionLiked(question.id)
//                                         ? FontAwesomeIcons.solidBookmark
//                                         : FontAwesomeIcons.bookmark,
//                                     'Love',
//                                     () => _handleLike(question.id),
//                                   ),
//                                 ),
//                                 if (isExplanation)
//                                   _buildIconButton(
//                                     FontAwesomeIcons.flag,
//                                     'Report',
//                                     () => _handleReport(questionIndex),
//                                   ),
//                               ]
//                               .map(
//                                 (btn) => Padding(
//                                   padding: const EdgeInsets.only(left: 0),
//                                   child: btn,
//                                 ),
//                               )
//                               .toList(),
//                     ),
//                 ],
//               ),
//               if (question.options != null)
//                 ...question.options!.asMap().entries.map(
//                   (entry) => OptionTile(
//                     questionIndex: questionIndex,
//                     optionIndex: entry.key,
//                     optionText: entry.value,
//                     isExplanation: isExplanation,
//                     questionVm: questionVm,
//                   ),
//                 ),
//               if (isExplanation) ...[
//                 const SizedBox(height: 16),
//                 ExplanationSection(explanation: question.explanation),
//               ],
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildIconButton(
//     IconData icon,
//     String tooltip,
//     VoidCallback onPressed,
//   ) {
//     return IconButton(
//       icon: Icon(icon, size: 18, color: Pallete.primary),
//       tooltip: tooltip,
//       padding: EdgeInsets.zero,
//       constraints: const BoxConstraints(),
//       visualDensity: VisualDensity.compact, // Add this line
//       onPressed: onPressed,
//     );
//   }

//   void _handleLike(int questionId) {
//     questionVm.toggleLikeQuestion(questionId);
//   }

//   void _handleReport(int questionIndex) async {
//     Get.bottomSheet(
//       ReportBottomSheet(
//         questionId: question.id,
//         onSubmit: (reason, comment) async {
//           // First dismiss the bottom sheet
//           Get.back();

//           // Then process the report
//           try {
//             await questionVm.reportQuestion(question.id, reason, comment);
//           } catch (e) {
//             //
//           }
//         },
//       ),
//       isScrollControlled: true,
//       enableDrag: true,
//     );
//   }

//   String cleanHtml(String rawHtml) {
//     String cleaned = rawHtml;

//     cleaned = cleaned.replaceAllMapped(RegExp(r'\$(.*?)\$', dotAll: true), (
//       match,
//     ) {
//       return '<tex>${match.group(1)}</tex>';
//     });

//     cleaned = cleaned.replaceAll(r'\n', ' ');
//     cleaned = cleaned.replaceAll('\n', ' ');
//     cleaned = cleaned.replaceAll(
//       RegExp(r'<br\s*/?>', caseSensitive: false),
//       ' ',
//     );
//     cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ').trim();
//     print(cleaned);
//     return cleaned;
//   }
// }
