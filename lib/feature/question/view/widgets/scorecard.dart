import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:qbank_bd/core/utils/pallete.dart';

class ScoreCard extends StatelessWidget {
  final String title;
  final int value;

  const ScoreCard({super.key, required this.title, required this.value});

  @override
  Widget build(BuildContext context) {
    // Calculate width based on screen width
    final screenWidth = Get.width;
    // Use 26% of screen width for each card, leaving more space between cards
    final cardWidth = screenWidth * 0.26;

    return Container(
      width: cardWidth,
      padding: const EdgeInsets.symmetric(vertical: 13),
      margin: const EdgeInsets.symmetric(horizontal: 2),
      decoration: BoxDecoration(
        color: Pallete.primary.withAlpha(20),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        children: [
          Text(
            "$value",
            style: const TextStyle(
              fontSize: 18, // Slightly smaller font
              color: Pallete.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 6), // Reduced spacing
          Text(
            title,
            style: TextStyle(
              fontSize: 13, // Slightly smaller font
              color: Get.theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}
