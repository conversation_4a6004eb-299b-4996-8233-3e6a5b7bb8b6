import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qbank_bd/core/common/widgets/q_buttons.dart';

class ExplanationSection extends StatelessWidget {
  final String? explanation;
  final RxBool showExplanation = false.obs;

  ExplanationSection({super.key, this.explanation});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Obx(() => _buildToggleButton()),
        Obx(() {
          if (!showExplanation.value) return const SizedBox();
          return _buildExplanationText();
        }),
      ],
    );
  }

  Widget _buildToggleButton() {
    return Container(
      decoration: BoxDecoration(
        color: Get.theme.primaryColor.withAlpha(40),
        borderRadius: BorderRadius.circular(8),
      ),
      child: QTextButton(
        text: showExplanation.value ? 'Hide Explanation' : 'Show Explanation',
        onPressed: showExplanation.toggle,
        textColor: Get.theme.primaryColor,
      ),
    );
  }

  Widget _buildExplanationText() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Get.theme.primaryColor.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Get.theme.primaryColor.withAlpha(80),
          width: 1,
        ),
      ),
      child: Text(
        (explanation == null || explanation!.isEmpty)
            ? 'No explanation available'
            : explanation!,
        style: TextStyle(color: Get.theme.colorScheme.onPrimary, fontSize: 14),
      ),
    );
  }
}
