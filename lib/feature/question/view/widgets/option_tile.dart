// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/feature/question/viewmodel/question_viewmodel.dart';

// class OptionTile extends StatelessWidget {
//   final int questionIndex;
//   final int optionIndex;
//   final String optionText;
//   final bool isExplanation;
//   final QuestionViewModel questionVm;

//   const OptionTile({
//     super.key,
//     required this.questionIndex,
//     required this.optionIndex,
//     required this.optionText,
//     required this.isExplanation,
//     required this.questionVm,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Obx(() {
//       final isSelected = questionVm.isOptionSelected(
//         questionIndex,
//         optionIndex,
//       );
//       final correctOption = questionVm.getCorrectOptionIndex(questionIndex);

//       // Default background color for the option container
//       Color backgroundColor = Get.theme.colorScheme.onSurface.withAlpha(14);

//       // Circle background color based on conditions
//       Color circleColor = Colors.transparent;
//       if (isExplanation) {
//         // In explanation mode
//         if (optionIndex == correctOption) {
//           if (isSelected) {
//             circleColor = Colors.green; // Correct answer that was selected
//           } else {
//             circleColor = Colors.blue; // Correct answer that wasn't selected
//           }
//         } else if (isSelected) {
//           circleColor = Colors.red; // Wrong answer
//         }
//       } else {
//         // In question mode
//         if (isSelected) {
//           circleColor = Colors.blue; // Selected answer
//         }
//       }

//       // Get the option letter (A, B, C, D, E)
//       final optionLetter = String.fromCharCode(
//         65 + optionIndex,
//       ); // ASCII 'A' is 65

//       return GestureDetector(
//         onTap:
//             isExplanation
//                 ? null
//                 : () => questionVm.selectOption(questionIndex, optionIndex),
//         child: Container(
//           margin: const EdgeInsets.symmetric(vertical: 6),
//           padding: const EdgeInsets.all(12),
//           decoration: BoxDecoration(
//             color: backgroundColor,
//             borderRadius: BorderRadius.circular(8),
//           ),
//           child: Row(
//             children: [
//               // Option letter circle
//               Container(
//                 width: 24,
//                 height: 24,
//                 margin: const EdgeInsets.only(right: 12),
//                 decoration: BoxDecoration(
//                   color: circleColor,
//                   shape: BoxShape.circle,
//                   border: Border.all(
//                     color: Get.theme.colorScheme.onPrimary.withAlpha(70),
//                     width: 1,
//                   ),
//                 ),
//                 child: Center(
//                   child: Text(
//                     optionLetter,
//                     style: TextStyle(
//                       color:
//                           circleColor == Colors.transparent
//                               ? Get.theme.colorScheme.onPrimary
//                               : Colors.white,
//                     ),
//                   ),
//                 ),
//               ),
//               Expanded(
//                 child: Text(
//                   optionText,
//                   style: TextStyle(
//                     color: Get.theme.colorScheme.onPrimary,
//                     fontSize: 15,
//                   ),
//                 ),
//               ),
//               // Removed the checkmark/X icon
//             ],
//           ),
//         ),
//       );
//     });
//   }
// }
