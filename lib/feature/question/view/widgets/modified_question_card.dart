// import 'dart:convert';
// import 'package:flutter/material.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter_html/flutter_html.dart';
// import 'package:flutter_html_math/flutter_html_math.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/core/common/model/question_model.dart';
// import 'package:qbank_bd/feature/question/viewmodel/question_viewmodel.dart';
// 
// class QuestionCard extends StatelessWidget {
//   final int questionIndex;
//   final QuestionModel question;
//   final bool isExplanation;
//   final bool isOfflineMode;
// 
//   final QuestionViewModel questionVm = Get.find<QuestionViewModel>();
// 
//   QuestionCard({
//     super.key,
//     required this.questionIndex,
//     required this.question,
//     this.isExplanation = false,
//     this.isOfflineMode = false,
//   });
// 
//   @override
//   Widget build(BuildContext context) {
//     return Card(
//       elevation: 0,
//       color: Get.theme.cardColor.withAlpha(100), // Lighter background
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
//       margin: const EdgeInsets.symmetric(vertical: 8),
//       child: Padding(
//         padding: const EdgeInsets.all(14),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Expanded(
//                   // Use Html widget with Math extension
//                   child: Html(
//                     data: cleanHtml(question.questionText),
//                     style: {
//                       // Optional: Add base styles if needed
//                       "body": Style(
//                         // Match font style from your HTML if needed
//                         // fontSize: FontSize(16.0), // Example
//                         // fontWeight: FontWeight.w500, // Example
//                       ),
//                       // Add styles for specific tags if needed
//                       "p": Style(
//                         // Inherit from body or set specific styles
//                       ),
//                       "table": Style(
//                         // Styles for tables
//                         backgroundColor: Colors.grey.shade100,
//                         border: Border.all(color: Colors.grey.shade300),
//                       ),
//                       "tr": Style(
//                         border: Border(
//                           bottom: BorderSide(color: Colors.grey.shade300),
//                         ),
//                       ),
//                       "th": Style(
//                         padding: HtmlPaddings.all(6),
//                         backgroundColor: Colors.grey.shade200,
//                         fontWeight: FontWeight.bold,
//                       ),
//                       "td": Style(
//                         padding: HtmlPaddings.all(6),
//                         alignment: Alignment.topLeft,
//                       ),
//                     },
//                     extensions: [
//                       // Add the Math extension
//                       MathHtmlExtension(),
//                       // Add TableHtmlExtension if complex tables require it
//                       // TableHtmlExtension(),
//                     ],
//                     onLinkTap: (url, _, __) {
//                       // Handle link taps if necessary
//                       print("Opening $url...");
//                       // Consider using url_launcher package
//                     },
//                     // onImageTap: (src, _, __) {
//                     //   // Handle image taps if necessary
//                     //   print("Image tapped: $src");
//                     // },
//                     // // Optional: Customize image rendering
//                     // customImageRenders: {
//                     //   // Example: Network image renderer
//                     //   networkSourceMatcher(): networkImageRender(
//                     //     loadingWidget: () => const CupertinoActivityIndicator(),
//                     //     errorWidget: () => const Icon(Icons.error_outline),
//                     //   ),
//                     //   // Add other renderers (asset, file, data) if needed
//                     // },
//                   ),
//                 ),
//                 // Only show action buttons if not in offline mode
//                 if (!isOfflineMode)
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.end,
//                     mainAxisSize: MainAxisSize.min,
//                     children:
//                         [
//                               Obx(
//                                 () => _buildIconButton(
//                                   questionVm.isQuestionLiked(question.id)
//                                       ? CupertinoIcons.heart_fill
//                                       : CupertinoIcons.heart,
//                                   'Love',
//                                   () => _handleLike(question.id),
//                                   iconColor: Colors.red,
//                                 ),
//                               ),
//                               if (isExplanation)
//                                 _buildIconButton(
//                                   CupertinoIcons.flag,
//                                   'Report',
//                                   () => _handleReport(questionIndex),
//                                   iconColor: Colors.blue,
//                                 ),
//                             ]
//                             .map(
//                               (btn) => Padding(
//                                 padding: const EdgeInsets.only(left: 0),
//                                 child: btn,
//                               ),
//                             )
//                             .toList(),
//                   ),
//               ],
//             ),
//             if (question.options != null)
//               ...question.options!.asMap().entries.map(
//                 (entry) => OptionTile(
//                   questionIndex: questionIndex,
//                   optionIndex: entry.key,
//                   optionText: entry.value,
//                   isExplanation: isExplanation,
//                   questionVm: questionVm,
//                 ),
//               ),
//             if (isExplanation) ...[
//               const SizedBox(height: 16),
//               ExplanationSection(explanation: question.explanation),
//             ],
//           ],
//         ),
//       ),
//     );
//   }
// 
//   Widget _buildIconButton(
//     IconData icon,
//     String tooltip,
//     VoidCallback onPressed, {
//     Color? iconColor,
//   }) {
//     return IconButton(
//       icon: Icon(icon, size: 20, color: iconColor),
//       tooltip: tooltip,
//       padding: EdgeInsets.zero,
//       constraints: const BoxConstraints(),
//       visualDensity: VisualDensity.compact, // Add this line
//       onPressed: onPressed,
//     );
//   }
// 
//   void _handleLike(int questionId) {
//     questionVm.toggleLikeQuestion(questionId);
//   }
// 
//   void _handleReport(int questionIndex) async {
//     Get.bottomSheet(
//       ReportBottomSheet(
//         questionId: question.id,
//         onSubmit: (reason, comment) async {
//           // First dismiss the bottom sheet
//           Get.back();
// 
//           // Then process the report
//           try {
//             await questionVm.reportQuestion(question.id, reason, comment);
//           } catch (e) {
//             // Handle error appropriately
//             print("Error reporting question: $e");
//             Get.snackbar("Error", "Failed to submit report.");
//           }
//         },
//       ),
//       isScrollControlled: true,
//       enableDrag: true,
//     );
//   }
// 
//   String cleanHtml(String raw) {
//     // Step 1: Try JSON decode if string is double-escaped
//     try {
//       // Check if it looks like a JSON string before attempting decode
//       if (raw.startsWith('\"') && raw.endsWith('\"')) {
//         raw = jsonDecode(raw);
//       }
//     } catch (_) {
//       // Not a JSON string or decode failed, move on
//     }
// 
//     // Step 2: Remove common HTML escape sequences and unnecessary escapes
//     // Be careful with replacements to avoid breaking valid content
//     return raw
//         .replaceAll(r'\"', '\"') // Unescape quotes
//         .replaceAll(r"\'", "'") // Unescape single quotes
//         // .replaceAll(r'\\', r'\') // Unescape backslashes - CAUTION: This might break LaTeX/MathML if not handled carefully
//         .replaceAll(r'\/', '/') // Unescape forward slashes
//         .trim(); // Trim whitespace
//   }
// }
// 
// class OptionTile extends StatelessWidget {
//   final int questionIndex;
//   final int optionIndex;
//   final String optionText;
//   final bool isExplanation;
//   final QuestionViewModel questionVm;
//   const OptionTile({
//     super.key,
//     required this.questionIndex,
//     required this.optionIndex,
//     required this.optionText,
//     required this.isExplanation,
//     required this.questionVm,
//   });
//   @override
//   Widget build(BuildContext context) => ListTile(title: Text(optionText));
// }
// 
// class ExplanationSection extends StatelessWidget {
//   final String? explanation;
//   const ExplanationSection({super.key, this.explanation});
//   @override
//   Widget build(BuildContext context) =>
//       explanation != null ? Html(data: explanation!) : const SizedBox.shrink();
// }
// 
// class ReportBottomSheet extends StatelessWidget {
//   final int questionId;
//   final Function(String, String) onSubmit;
//   const ReportBottomSheet({
//     super.key,
//     required this.questionId,
//     required this.onSubmit,
//   });
//   @override
//   Widget build(BuildContext context) => Container(); // Placeholder
// }
