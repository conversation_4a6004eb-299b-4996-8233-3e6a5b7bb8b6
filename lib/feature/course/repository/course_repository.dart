import 'package:qbank_bd/feature/course/model/course_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class CourseRepository {
  final _supabase = Supabase.instance.client;

  Future<List<CourseModel>> fetchCoursesWithQbanks(String userId) async {
    final response = await _supabase
        .from('user_qbanks')
        .select('''
          qbankId,
          done,
          qbanks (
            id,
            name,
            coverImage,
            color,
            questionCount,
            hasSubtopic
          )
        ''')
        .eq('userId', userId);

    return response
        .map<CourseModel>((json) => CourseModel.fromJson(json))
        .toList();
  }
}
