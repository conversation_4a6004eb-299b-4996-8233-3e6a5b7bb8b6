import 'package:qbank_bd/core/common/model/qbank_model.dart';

class CourseModel {
  final String qbankId;
  final int done;
  final QbankModel qbank;

  CourseModel({required this.qbankId, required this.done, required this.qbank});

  factory CourseModel.fromJson(Map<String, dynamic> json) {
    return CourseModel(
      qbankId: json['qbankId'],
      done: json['done'],
      // Fixed: Use 'qbanks' (not 'qbank') and call fromMap (not fromJson)
      qbank: QbankModel.fromMap(json['qbanks']),
    );
  }
}
