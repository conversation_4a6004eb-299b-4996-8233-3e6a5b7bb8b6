import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:get/instance_manager.dart';
import 'package:qbank_bd/feature/auth/model/user_model.dart';
import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';
import 'package:qbank_bd/feature/course/model/course_model.dart';
import 'package:qbank_bd/feature/course/repository/course_repository.dart';

class CourseViewModel extends GetxController {
  final CourseRepository _repository = CourseRepository();

  final coursesWithQbanks = <CourseModel>[].obs;
  final isLoading = false.obs;

  final _userController = Get.find<AuthController>();
  UserModel get user => _userController.currentUser.value ?? UserModel.empty();

  @override
  void onInit() {
    super.onInit();
    fetchCoursesWithQbanks();
  }

  Future<void> fetchCoursesWithQbanks() async {
    isLoading.value = true;
    try {
      final fetchedCourses = await _repository.fetchCoursesWithQbanks(user.id);
      coursesWithQbanks.assignAll(fetchedCourses);
    } catch (_) {
      coursesWithQbanks.clear();
    } finally {
      isLoading.value = false;
    }
  }
}
