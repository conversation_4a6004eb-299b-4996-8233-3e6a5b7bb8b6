import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';

class CourseCardShimmer extends StatelessWidget {
  const CourseCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      child: Shimmer.fromColors(
        baseColor: Get.theme.colorScheme.onPrimary.withAlpha(40),
        highlightColor: Get.theme.colorScheme.onPrimary.withAlpha(80),
        child: Container(
          // Height equals width for 1:1 aspect ratio
          decoration: BoxDecoration(
            color: Get.theme.colorScheme.onPrimary.withAlpha(60),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Stack(
            children: [
              // Main image area
              Container(
                decoration: BoxDecoration(
                  color: Get.theme.colorScheme.onPrimary.withAlpha(60),
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
