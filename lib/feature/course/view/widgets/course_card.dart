import 'package:flutter/material.dart';
import 'package:get/route_manager.dart';
import 'package:qbank_bd/core/utils/utils.dart';
import 'package:qbank_bd/feature/course/model/course_model.dart';
import 'package:qbank_bd/feature/qbanks/view/widgets/qbank_card.dart';

import 'dart:ui';

class CourseCard extends StatefulWidget {
  final CourseModel course;

  const CourseCard({super.key, required this.course});

  @override
  State<CourseCard> createState() => _CourseCardState();
}

class _CourseCardState extends State<CourseCard>
    with SingleTickerProviderStateMixin {
  late final AnimationController _animationController;
  late final Animation<double> _animation;
  late final Animation<double> _percentageAnimation;
  late final double progressPercentage;

  @override
  void initState() {
    super.initState();

    progressPercentage =
        widget.course.qbank.questionCount == 0
            ? 0.0
            : (widget.course.done / widget.course.qbank.questionCount);

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _animation = Tween<double>(begin: 0, end: progressPercentage).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _percentageAnimation = Tween<double>(
      begin: 0,
      end: progressPercentage * 100,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 360;

    return Stack(
      children: [
        QbankCard(subject: widget.course.qbank),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: ClipRRect(
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(12),
              bottomRight: Radius.circular(12),
            ),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Get.theme.colorScheme.surface,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Progress text
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '${widget.course.qbank.questionCount} of ${widget.course.done}',
                            style: TextStyle(
                              color: Get.theme.colorScheme.onSurface,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            'Completed',
                            style: TextStyle(
                              color: Get.theme.colorScheme.onSurface,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),

                    Stack(
                      alignment: Alignment.center,
                      children: [
                        AnimatedBuilder(
                          animation: _animation,
                          builder: (context, child) {
                            return SizedBox(
                              width: isSmallScreen ? 30 : 34,
                              height: isSmallScreen ? 30 : 34,
                              child: CircularProgressIndicator(
                                value: _animation.value,
                                strokeWidth: isSmallScreen ? 4.0 : 5.0,
                                strokeCap: StrokeCap.round,
                                color: hexToColor(widget.course.qbank.color),
                                backgroundColor: Colors.grey.withAlpha(100),
                              ),
                            );
                          },
                        ),
                        AnimatedBuilder(
                          animation: _percentageAnimation,
                          builder: (context, child) {
                            return Text(
                              '${_percentageAnimation.value.toInt()}%',
                              style: TextStyle(
                                color: Get.theme.colorScheme.onSurface,
                                fontWeight: FontWeight.w500,
                                fontSize: isSmallScreen ? 8 : 9.5,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
