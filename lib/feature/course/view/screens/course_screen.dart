import 'package:flutter/material.dart';
import 'package:qbank_bd/core/router/app_router.dart';
import 'package:qbank_bd/feature/course/view/widgets/course_card.dart';
import 'package:qbank_bd/feature/course/view/widgets/course_card_shimmer.dart';
import 'package:qbank_bd/feature/course/viewmodel/course_viewmodel.dart';
import 'package:get/get.dart';
import 'package:qbank_bd/feature/qbanks/viewmodel/quiz_viewmodel.dart';
import 'package:qbank_bd/feature/qbanks/view/widgets/reusable_appbar.dart';

class CourseScreen extends StatefulWidget {
  const CourseScreen({super.key});
  @override
  State<CourseScreen> createState() => _CourseScreenState();
}

class _CourseScreenState extends State<CourseScreen> {
  final courseViewmodel = Get.put(CourseViewModel());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: ReusableAppbar(title: 'Courses'),
      body: Obx(() {
        // Show shimmer only if data is still loading
        if (courseViewmodel.isLoading.value) {
          return _buildShimmerLoading();
        }

        if (courseViewmodel.coursesWithQbanks.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.school_outlined,
                  size: 80,
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(100),
                ),
                const SizedBox(height: 16),
                Text(
                  'You are not enrolled in any course.',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return GridView.builder(
          padding: const EdgeInsets.fromLTRB(12, 12, 12, 14),
          itemCount: courseViewmodel.coursesWithQbanks.length,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: _getCrossAxisCount(),
            mainAxisSpacing: 14,
            crossAxisSpacing: 14,
          ),
          itemBuilder: (context, index) {
            return GestureDetector(
              onTap: () async {
                final course = courseViewmodel.coursesWithQbanks[index];
                Get.toNamed(AppRoutes.quiz, arguments: course.qbank);
              },
              child: CourseCard(
                course: courseViewmodel.coursesWithQbanks[index],
              ),
            );
          },
        );
      }),
    );
  }

  Widget _buildShimmerLoading() {
    return GridView.builder(
      //padding: const EdgeInsets.fromLTRB(12, 0, 12, 14),
      itemCount: 6,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _getCrossAxisCount(),
        mainAxisSpacing: 0,
        //crossAxisSpacing: 16,
      ),
      itemBuilder: (context, index) => const CourseCardShimmer(),
    );
  }

  int _getCrossAxisCount() {
    final screenWidth = Get.width;
    if (screenWidth > 1200) return 4; // Very large screens
    if (screenWidth > 800) return 3; // Large screens/tablets
    return 2; // Small screens/phones
  }
}
