import 'package:qbank_bd/feature/auth/model/user_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class LeaderboardRepository {
  static final SupabaseClient _client = Supabase.instance.client;

  Future<List<UserModel>> fetchPaginatedLeaderboard({
    required String levelId,
    required int page,
    int limit = 10,
    int maxUsers = 200,
  }) async {
    try {
      final int offset = page * limit;

      // Don't fetch beyond the maximum number of users
      if (offset >= maxUsers) {
        return [];
      }

      final response = await _client
          .from('users')
          .select()
          .eq('level', levelId)
          .order('points', ascending: false)
          .range(offset, offset + limit - 1);

      return (response as List)
          .map((item) => UserModel.fromMap(item as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Error fetching leaderboard: $e');
      return [];
    }
  }
}
