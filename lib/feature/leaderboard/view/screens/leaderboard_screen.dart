// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/feature/leaderboard/view/widgets/scrollable_users.dart';
// import 'package:qbank_bd/feature/leaderboard/view/widgets/top_three_users.dart';
// import 'package:qbank_bd/feature/leaderboard/viewmodel/leaderboard_viewmodel.dart';
// import 'package:qbank_bd/feature/qbanks/view/widgets/reusable_appbar.dart';

// class LeaderboardScreen extends StatelessWidget {
//   const LeaderboardScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     // Initialize the viewmodel
//     Get.put(LeaderboardViewModel());
//     final controller = Get.find<LeaderboardViewModel>();

//     return Scaffold(
//       appBar: ReusableAppbar(title: 'Leaderboard'),
//       body: Obx(() {
//         if (controller.isLoading.value) {
//           return const Center(child: CircularProgressIndicator());
//         }

//         return SafeArea(
//           child: Column(
//             children: [
//               const SizedBox(height: 20),
//               const TopThreeUsers(),
//               const SizedBox(height: 20),
//               Expanded(child: ScrollableLeaderboard()),
//             ],
//           ),
//         );
//       }),
//     );
//   }
// }
