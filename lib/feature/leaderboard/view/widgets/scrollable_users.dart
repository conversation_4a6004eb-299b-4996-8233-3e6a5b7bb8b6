// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/core/common/widgets/circular_profile_image.dart';
// import 'package:qbank_bd/feature/leaderboard/viewmodel/leaderboard_viewmodel.dart';

// class ScrollableLeaderboard extends StatelessWidget {
//   final controller = Get.put(LeaderboardViewModel());

//   ScrollableLeaderboard({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Obx(() {
//       // Remove the loading check here since it's handled in the parent
//       return ListView.builder(
//         controller: controller.scrollController,
//         itemCount:
//             controller.remainingUsers.length + (controller.hasMoreData ? 1 : 0),
//         padding: const EdgeInsets.symmetric(horizontal: 16),
//         itemBuilder: (context, index) {
//           if (index == controller.remainingUsers.length) {
//             return Obx(() {
//               return controller.isLoadingMore.value
//                   ? const Padding(
//                     padding: EdgeInsets.all(8.0),
//                     child: Center(child: CircularProgressIndicator()),
//                   )
//                   : const SizedBox();
//             });
//           }

//           final user = controller.remainingUsers[index];
//           // Add 4 to the index to get the correct rank (since we skipped top 3)
//           final rank = index + 4;

//           return Container(
//             margin: const EdgeInsets.symmetric(vertical: 6),
//             padding: const EdgeInsets.all(16),
//             decoration: BoxDecoration(
//               color: Get.theme.colorScheme.surface,
//               borderRadius: BorderRadius.circular(12),
//               //border: Border.all(color: Colors.grey.withAlpha(40), width: 1),
//             ),
//             child: Row(
//               children: [
//                 CircularProfileImage(imageUrl: user.profileImage, radius: 20),
//                 const SizedBox(width: 16),
//                 Expanded(child: Text("#$rank   ${user.name}")),
//                 Text("${user.points} points"),
//               ],
//             ),
//           );
//         },
//       );
//     });
//   }
// }
