// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/feature/auth/model/user_model.dart';
// import 'package:qbank_bd/feature/leaderboard/view/widgets/user_circle.dart';
// import 'package:qbank_bd/feature/leaderboard/viewmodel/leaderboard_viewmodel.dart';

// class TopThreeUsers extends StatelessWidget {
//   const TopThreeUsers({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final controller = Get.find<LeaderboardViewModel>();
//     final spacing = Get.width * 0.1; // 8% of screen width for spacing

//     return Obx(() {
//       if (controller.users.isEmpty) {
//         return const SizedBox(height: 120);
//       }

//       // Ensure we have 3 users, or pad with empty users
//       final List<UserModel?> topThree = List.filled(3, null);
//       for (int i = 0; i < controller.topThreeUsers.length && i < 3; i++) {
//         topThree[i] = controller.topThreeUsers[i];
//       }

//       return LayoutBuilder(
//         builder: (context, constraints) {
//           return SingleChildScrollView(
//             scrollDirection: Axis.horizontal,
//             child: ConstrainedBox(
//               constraints: BoxConstraints(minWidth: constraints.maxWidth),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 crossAxisAlignment: CrossAxisAlignment.end,
//                 children: [
//                   // Second place
//                   if (topThree[1] != null)
//                     UserCircle(
//                       rank: 2,
//                       username: topThree[1]!.name,
//                       points: topThree[1]!.points,
//                       radius: 30,
//                       profileImage: topThree[1]!.profileImage,
//                     ),
//                   SizedBox(width: spacing),

//                   // First place (larger)
//                   if (topThree[0] != null)
//                     UserCircle(
//                       rank: 1,
//                       username: topThree[0]!.name,
//                       points: topThree[0]!.points,
//                       radius: 40, // Larger radius for first place
//                       profileImage: topThree[0]!.profileImage,
//                     ),
//                   SizedBox(width: spacing),

//                   // Third place
//                   if (topThree[2] != null)
//                     UserCircle(
//                       rank: 3,
//                       username: topThree[2]!.name,
//                       points: topThree[2]!.points,
//                       radius: 30,
//                       profileImage: topThree[2]!.profileImage,
//                     ),
//                 ],
//               ),
//             ),
//           );
//         },
//       );
//     });
//   }
// }
