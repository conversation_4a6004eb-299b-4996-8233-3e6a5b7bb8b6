import 'package:flutter/material.dart';
import 'package:get/route_manager.dart';
import 'package:qbank_bd/core/common/widgets/circular_profile_image.dart';

class UserCircle extends StatelessWidget {
  final int rank;
  final String username;
  final int points;
  final double radius;
  final String? profileImage;

  const UserCircle({
    super.key,
    required this.rank,
    required this.username,
    required this.points,
    this.radius = 30,
    this.profileImage,
  });

  Color get badgeColor {
    switch (rank) {
      case 1:
        return Color(0xFFFFD700);
      case 2:
        return Color(0xFFC0C0C0);
      case 3:
        return Color(0xFFCD7F32);
      default:
        return Color(0xFFC0C0C0);
    }
  }

  String get userInitial {
    if (username.isEmpty) return "U";
    return username[0].toUpperCase();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Stack(
          alignment: Alignment.bottomRight,
          children: [
            CircularProfileImage(imageUrl: profileImage, radius: radius),
            CircularProfileImage(imageUrl: profileImage, radius: radius),
          ],
        ),
        const SizedBox(height: 6),
        SizedBox(
          width: radius * 2.5,
          child: Text(
            username,
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
        Text('$points points', style: TextStyle(fontSize: 12)),
      ],
    );
  }
}
