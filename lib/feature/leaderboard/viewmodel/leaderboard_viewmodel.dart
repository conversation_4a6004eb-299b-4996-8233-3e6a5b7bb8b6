// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/feature/auth/model/user_model.dart';
// import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';
// import 'package:qbank_bd/feature/leaderboard/repository/leaderboard_repository.dart';

// class LeaderboardViewModel extends GetxController {
//   final users = <UserModel>[].obs;
//   final isLoading = false.obs;
//   final isLoadingMore = false.obs;
//   final scrollController = ScrollController();

//   final _repository = LeaderboardRepository();
//   final _authController = Get.find<AuthController>();

//   int _page = 0;
//   bool _hasMore = true;
//   final int _maxUsers = 200;

//   UserModel get currentUser => _authController.currentUser.value!;

//   // Get top three users from the main list
//   List<UserModel> get topThreeUsers =>
//       users.isNotEmpty ? users.take(3).toList() : [];

//   // Get users after the top three
//   List<UserModel> get remainingUsers =>
//       users.length > 3 ? users.skip(3).toList() : [];

//   @override
//   void onInit() {
//     super.onInit();
//     initializeData();
//   }

//   @override
//   void onClose() {
//     scrollController.dispose();
//     super.onClose();
//   }

//   Future<void> initializeData() async {
//     loadInitialUsers();

//     scrollController.addListener(() {
//       if (scrollController.position.pixels >=
//           scrollController.position.maxScrollExtent - 200) {
//         loadMoreUsers();
//       }
//     });
//   }

//   Future<void> loadInitialUsers() async {
//     _page = 0;
//     _hasMore = true;
//     users.clear();

//     isLoading.value = true;
//     try {
//       final result = await _repository.fetchPaginatedLeaderboard(
//         levelId: currentUser.level,
//         page: _page,
//       );

//       if (result.isNotEmpty) {
//         users.assignAll(result);
//       }

//       _hasMore = result.length == 10;
//     } catch (e) {
//       print('Error loading initial users: $e');
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   Future<void> loadMoreUsers() async {
//     if (!_hasMore || isLoadingMore.value) return;
//     if (users.length >= _maxUsers) {
//       _hasMore = false;
//       return;
//     }

//     isLoadingMore.value = true;
//     _page++;

//     try {
//       final result = await _repository.fetchPaginatedLeaderboard(
//         levelId: currentUser.level,
//         page: _page,
//         maxUsers: _maxUsers,
//       );
//       users.addAll(result);
//       _hasMore = result.length == 10 && users.length < _maxUsers;
//     } catch (e) {
//       print('Error loading more users: $e');
//       _page--;
//     } finally {
//       isLoadingMore.value = false;
//     }
//   }

//   bool get hasMoreData => _hasMore;
// }
