// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/core/common/model/question_model.dart';
// import 'package:qbank_bd/core/common/model/qbank_model.dart';
// import 'package:qbank_bd/core/common/model/quiz_model.dart';
// import 'package:qbank_bd/feature/auth/model/user_model.dart';
// import 'package:qbank_bd/core/utils/utils.dart';
// import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';
// import 'package:qbank_bd/feature/qbanks/repository/qbanks_repository.dart';
// import 'package:qbank_bd/feature/qbanks/repository/quiz_repository.dart';
// import 'package:qbank_bd/feature/question/repository/question_repository.dart';
// import 'package:sqflite/sqflite.dart';
// import 'package:qbank_bd/core/services/database_helper.dart';

// class QuizViewModel extends GetxController {
//   final String subtopicId;

//   QuizViewModel({required this.subtopicId});

//   final userQuizzes = <String>[].obs;
//   final quizzes = <QuizModel>[].obs;
//   final isLoading = false.obs;
//   final isLoadingMore = false.obs;
//   final _downloadingQuizzes = <String>{}.obs;

//   final _userController = Get.find<AuthController>();
//   final _repository = QuizRepository();
//   final _questionRepo = QuestionRepository();

//   int _page = 0;
//   bool _hasMore = true;

//   UserModel get user => _userController.currentUser.value ?? UserModel.empty();
//   bool get hasMoreData => _hasMore;

//   @override
//   void onInit() {
//     super.onInit();
//     initializeData();
//   }

//   Future<void> initializeData() async {
//     await _withLoading(() async {
//       await fetchUsersQuizzes();
//       await loadInitialQuizzes();
//     });
//   }

//   Future<void> loadInitialQuizzes() async {
//     _page = 0;
//     _hasMore = true;
//     quizzes.clear();
//     await _fetchPaginatedQuizzes();
//   }

//   Future<void> fetchUsersQuizzes() async {
//     final result = await _repository.fetchQuizzesArrayById(
//       "${user.id}$subtopicId",
//     );
//     print(result);
//     result.match(
//       (e) => userQuizzes.clear(),
//       (data) => userQuizzes.assignAll(data),
//     );
//   }

//   Future<void> loadMoreQuizzes() async {
//     if (!_hasMore || isLoadingMore.value) return;

//     isLoadingMore.value = true;
//     _page++;

//     await _fetchPaginatedQuizzes().whenComplete(() {
//       isLoadingMore.value = false;
//     });
//   }

//   Future<void> _fetchPaginatedQuizzes() async {
//     final result = await _repository.fetchPaginatedQuizzes(
//       subtopicId: subtopicId,
//       page: _page,
//     );

//     result.match(
//       (e) {
//         _error('Failed to load quizzes');
//         if (_page > 0) _page--;
//       },
//       (data) {
//         quizzes.addAll(data);
//         _hasMore = data.isNotEmpty;
//       },
//     );
//   }

//   Future<bool> isQuizDownloaded(String quizId) async {
//     final db = await DatabaseHelper.instance.database;
//     final result = await db.query(
//       'quizzes',
//       where: 'id = ? AND downloadedAt IS NOT NULL',
//       whereArgs: [quizId],
//     );
//     return result.isNotEmpty;
//   }

//   Future<bool> downloadQuiz(String quizId) async {
//     if (_downloadingQuizzes.contains(quizId)) return false;
//     _downloadingQuizzes.add(quizId);

//     try {
//       final quiz = quizzes.firstWhere((q) => q.id == quizId);

//       Get.dialog(
//         const Center(child: CircularProgressIndicator()),
//         barrierDismissible: false,
//       );

//       final questions = await _questionRepo.fetchQuestionsByquizId(quizId);
//       Get.back();

//       if (questions.isEmpty) throw Exception('No questions found.');

//       await _saveQuizToLocalDB(quiz, questions);

//       final index = quizzes.indexWhere((q) => q.id == quizId);
//       if (index != -1) {
//         quizzes[index] = quiz.copyWith(downloadedAt: DateTime.now());
//         quizzes.refresh();
//       }

//       return true;
//     } catch (_) {
//       return false;
//     } finally {
//       _downloadingQuizzes.remove(quizId);
//     }
//   }

//   Future<void> _saveQuizToLocalDB(
//     QuizModel quiz,
//     List<QuestionModel> questions,
//   ) async {
//     final db = await DatabaseHelper.instance.database;

//     await db.transaction((txn) async {
//       final existing = await txn.query(
//         'quizzes',
//         where: 'id = ?',
//         whereArgs: [quiz.id],
//       );

//       final timestamp = DateTime.now().toIso8601String();

//       if (existing.isNotEmpty) {
//         await txn.update(
//           'quizzes',
//           {'downloadedAt': timestamp},
//           where: 'id = ?',
//           whereArgs: [quiz.id],
//         );
//       } else {
//         await txn.insert(
//           'quizzes',
//           quiz.copyWith(downloadedAt: DateTime.now()).toMap(),
//         );
//       }

//       for (var q in questions) {
//         await txn.insert(
//           'questions',
//           q.toMap(),
//           conflictAlgorithm: ConflictAlgorithm.replace,
//         );
//       }
//     });
//   }

//   bool isDownloadingQuiz(String quizId) => _downloadingQuizzes.contains(quizId);

//   Future<void> _withLoading(Future<void> Function() task) async {
//     isLoading.value = true;
//     try {
//       await task();
//     } catch (_) {
//       _error('Something went wrong');
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   void _error(String message) {
//     showCustomSnackbar(title: 'Error', message: message, isSuccess: false);
//   }
// }
