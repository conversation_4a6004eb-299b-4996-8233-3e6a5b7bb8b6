// import 'package:get/get.dart';
// import 'package:qbank_bd/core/common/model/qbank_model.dart';
// import 'package:qbank_bd/feature/auth/model/user_model.dart';
// import 'package:qbank_bd/core/utils/utils.dart';
// import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';
// import 'package:qbank_bd/feature/qbanks/repository/qbanks_repository.dart';

// class QbankViewModel extends GetxController {
//   final subjects = <QbankModel>[].obs;
//   final categories = <String>[].obs;
//   final isLoading = false.obs;

//   final _userController = Get.find<AuthController>();

//   UserModel get user => _userController.currentUser.value ?? UserModel.empty();
//   final _repository = Qbanksrepository();

//   // Add this method to clear all data
//   void clearAllData() {
//     subjects.clear();
//     categories.clear();
//   }

//   @override
//   void onInit() {
//     super.onInit();
//     initializeData();
//   }

//   Future<void> initializeData() async {
//     clearAllData();

//     await fetchCategories();
//     await fetchSubjects();
//   }

//   Future<void> fetchSubjects() async {
//     await _runWithLoading(() async {
//       final result = await _repository.fetchSubjectsByLevel(user.level);
//       result.match((e) => {}, (data) => subjects.assignAll(data));
//     });
//   }

//   Future<List<QbankModel>> fetchSubjectsBySubtopic(String subjectId) async {
//     final result = await _repository.fetchSubjectsByLevel(subjectId);
//     return result.match((e) => [], (data) => data);
//   }

//   Future<void> fetchCategories() async {
//     if (user.level.isEmpty) {
//       await Future.delayed(const Duration(milliseconds: 300));
//       if (user.level.isEmpty) {
//         return;
//       }
//     }

//     final result = await _repository.fetchCategoriesByLevel(user.level);
//     result.match((e) => {}, (data) {
//       categories.assignAll(data);
//     });
//   }

//   void _error(String message) =>
//       showCustomSnackbar(title: 'Error', message: message, isSuccess: false);

//   Future<void> _runWithLoading(Future<void> Function() task) async {
//     isLoading.value = true;
//     try {
//       await task();
//     } catch (_) {
//       _error('Something went wrong');
//     } finally {
//       isLoading.value = false;
//     }
//   }
// }
