import 'package:fpdart/fpdart.dart';
import 'package:qbank_bd/core/common/model/qbank_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class Qbanksrepository {
  static final SupabaseClient _client = Supabase.instance.client;

  Future<Either<String, List<QbankModel>>> fetchSubjectsByLevel(
    String levelId,
  ) async {
    try {
      final response = await _client.from('qbanks').select().contains(
        'levelId',
        [levelId],
      );

      return right(
        (response as List)
            .map((item) => QbankModel.fromMap(item as Map<String, dynamic>))
            .toList(),
      );
    } catch (e) {
      return left('Error fetching subjects: ${e.toString()}');
    }
  }

  Future<Either<String, List<String>>> fetchCategoriesByLevel(String id) async {
    try {
      final response =
          await _client.from('categories').select('name').eq('id', id).single();

      return right(
        (response['name'] as List).map((item) => item.toString()).toList(),
      );
    } catch (e) {
      return left('Error fetching categories: ${e.toString()}');
    }
  }
}
