import 'package:fpdart/fpdart.dart';
import 'package:qbank_bd/core/common/model/quiz_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class QuizRepository {
  static final SupabaseClient _client = Supabase.instance.client;

  Future<Either<String, List<String>>> fetchQuizzesArrayById(
    String subtopicId,
  ) async {
    try {
      final response =
          await _client
              .from('user_qbanks')
              .select('quizzes')
              .eq('id', subtopicId)
              .maybeSingle();

      final quizzes =
          (response?['quizzes'] as List?)?.map((e) => e.toString()).toList() ??
          [];

      return right(quizzes);
    } catch (e) {
      return left('Error fetching quizzes: ${e.toString()}');
    }
  }

  Future<Either<String, List<QuizModel>>> fetchPaginatedQuizzes({
    required String subtopicId,
    required int page,
    int limit = 10,
  }) async {
    try {
      final int offset = page * limit;

      final response = await _client
          .from('quizzes')
          .select()
          .eq('subtopicId', subtopicId)
          .range(offset, offset + limit - 1)
          .order('year', ascending: false);

      return right(
        (response as List)
            .map((item) => QuizModel.fromMap(item as Map<String, dynamic>))
            .toList(),
      );
    } catch (e) {
      return left('Error fetching quizzes: ${e.toString()}');
    }
  }
}
