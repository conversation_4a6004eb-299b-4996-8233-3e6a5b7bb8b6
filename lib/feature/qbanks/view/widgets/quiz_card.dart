import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:qbank_bd/core/common/model/quiz_model.dart';

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

class QuizCard extends StatelessWidget {
  final QuizModel quiz;
  final bool isCompleted;
  final bool isDownloaded;
  final bool isDownloading;
  final VoidCallback? onDownload;

  const QuizCard({
    super.key,
    required this.quiz,
    this.isCompleted = false,
    this.isDownloaded = false,
    this.isDownloading = false,
    this.onDownload,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(12, 10, 12, 12),
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title & Actions
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Quiz name
              Expanded(
                child: Text(
                  quiz.name,
                  style: const TextStyle(
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              // Completed check
              if (isCompleted) ...[
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.green.withAlpha(20),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    FontAwesomeIcons.circleCheck,
                    color: Colors.green,
                    size: 18,
                  ),
                ),
                const SizedBox(width: 12),
              ],
              // Download icon or loader
              GestureDetector(
                onTap: onDownload,
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Get.theme.colorScheme.onSurface.withAlpha(20),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child:
                      isDownloading
                          ? SizedBox(
                            width: 18,
                            height: 18,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Get.theme.colorScheme.onPrimary,
                            ),
                          )
                          : Icon(
                            isDownloaded
                                ? FontAwesomeIcons.trash
                                : FontAwesomeIcons.arrowDown,
                            color:
                                isDownloaded
                                    ? Colors.pink
                                    : Get.theme.colorScheme.onPrimary.withAlpha(
                                      220,
                                    ),
                            size: 16,
                          ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Info badges
          Row(
            children: [
              _InfoBadge(
                icon: Icons.access_time,
                label: '${quiz.duration} Minutes',
                color: Colors.pink,
              ),
              const Spacer(),
              _InfoBadge(
                icon: Icons.help_outline,
                label: '${quiz.questionCount} Questions',
                color: Colors.green,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _InfoBadge extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;

  const _InfoBadge({
    required this.icon,
    required this.label,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
      decoration: BoxDecoration(
        color: color.withAlpha(20),
        borderRadius: BorderRadius.circular(7),
      ),
      child: Row(
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: color, fontSize: 14),
          ),
        ],
      ),
    );
  }
}
