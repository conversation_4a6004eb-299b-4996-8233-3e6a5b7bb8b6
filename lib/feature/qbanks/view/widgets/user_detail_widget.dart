import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qbank_bd/core/utils/utils.dart';

class UserDetailWidget extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  const UserDetailWidget({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    // Calculate width based on screen width
    // Using 28% of screen width with some padding consideration
    final widgetWidth =
        (Get.width - 100) / 3; // 64 accounts for outer padding and spacing

    return Container(
      width: widgetWidth,
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Top Text
          Text(
            title,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w500,
              color: Get.theme.colorScheme.onSurface,
            ),
          ),

          SizedBox(height: 4),

          // Rounded rectangle background covering both icon and text
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 20),
              SizedBox(width: 10),
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  color: Get.theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
