import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';

class SectionShimmer extends StatelessWidget {
  const SectionShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    // Get screen width to make responsive adjustments
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    // Calculate card width based on screen size (similar to SubjectCard)
    final cardWidth = isSmallScreen ? 120.0 : 140.0;

    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Shimmer.fromColors(
        baseColor: Get.theme.colorScheme.onPrimary.withAlpha(40),
        highlightColor: Get.theme.colorScheme.onPrimary.withAlpha(40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Row with title and "See All" button
            Padding(
              padding: const EdgeInsets.only(left: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Section title shimmer - match text style from SectionWidget
                  Container(
                    width: screenWidth * 0.3, // Responsive width
                    height: 16,
                    decoration: BoxDecoration(
                      color: Get.theme.colorScheme.onPrimary.withAlpha(60),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),

                  // "See All" button shimmer - match TextButton from SectionWidget
                  Padding(
                    padding: const EdgeInsets.only(right: 12),
                    child: Container(
                      width: 60,
                      height: 15,
                      decoration: BoxDecoration(
                        color: Get.theme.colorScheme.onPrimary.withAlpha(60),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),

            // Horizontal scroll items shimmer - match ListView from SectionWidget
            SizedBox(
              height: 160,
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 12),
                itemCount:
                    screenWidth ~/ (cardWidth + 12), // Responsive item count
                separatorBuilder: (_, __) => const SizedBox(width: 12),
                itemBuilder:
                    (_, index) => Padding(
                      padding: EdgeInsets.only(
                        left: index == 0 ? 0 : 0,
                        right:
                            index == (screenWidth ~/ (cardWidth + 12) - 1)
                                ? 0
                                : 0,
                      ),
                      child: _buildSubjectCardShimmer(cardWidth),
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubjectCardShimmer(double width) {
    return Container(
      width: width,
      height: 160,
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.onPrimary.withAlpha(60),
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }
}
