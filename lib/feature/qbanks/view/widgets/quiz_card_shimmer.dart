import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class QuizCardShimmer extends StatelessWidget {
  const QuizCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    // Define card width as 90% of screen width
    final cardWidth = screenWidth * 0.9;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      child: Shimmer.fromColors(
        baseColor: Colors.grey.shade300, // Neutral base for shimmer
        highlightColor: Colors.grey.shade100, // Bright highlight for flash
        period: const Duration(milliseconds: 1200), // Smooth shimmer cycle
        direction: ShimmerDirection.ltr, // Left-to-right flash
        child: Container(
          width: cardWidth,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.shade300, // Matches baseColor
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title row with icon placeholder
              Row(
                children: [
                  Expanded(
                    child: Container(height: 20, color: Colors.grey.shade300),
                  ),
                  const SizedBox(width: 12),
                  Container(width: 30, height: 30, color: Colors.grey.shade300),
                ],
              ),
              const SizedBox(height: 12),
              // Info badges row
              Row(
                children: [
                  Container(
                    width: isSmallScreen ? 90 : 110,
                    height: 28,
                    color: Colors.grey.shade300,
                  ),
                  const Spacer(),
                  Container(
                    width: isSmallScreen ? 90 : 110,
                    height: 28,
                    color: Colors.grey.shade300,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
