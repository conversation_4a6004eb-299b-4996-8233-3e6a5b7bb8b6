// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/core/common/model/qbank_model.dart';
// import 'package:qbank_bd/core/router/app_router.dart';
// import 'package:qbank_bd/core/utils/pallete.dart';
// import 'package:qbank_bd/feature/qbanks/view/widgets/qbank_card.dart';
// import 'package:qbank_bd/feature/qbanks/viewmodel/qbank_viewmodel.dart';
// import 'package:qbank_bd/feature/qbanks/viewmodel/quiz_viewmodel.dart';

// class SectionWidget extends StatelessWidget {
//   final String title;
//   final List<QbankModel> subjects;

//   const SectionWidget({super.key, required this.title, required this.subjects});

//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.only(bottom: 0),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // Header Row
//           Padding(
//             padding: const EdgeInsets.only(left: 12),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   title,
//                   style: TextStyle(
//                     color: Theme.of(context).colorScheme.onSurface,
//                     fontSize: 16,
//                     fontWeight: FontWeight.w500,
//                   ),
//                 ),
//                 TextButton(
//                   onPressed: () {
//                     Get.toNamed(AppRoutes.allSubject, arguments: subjects);
//                   },
//                   child: Text(
//                     "See All",
//                     style: TextStyle(color: Pallete.primary, fontSize: 15),
//                   ),
//                 ),
//               ],
//             ),
//           ),

//           //SizedBox(height: 6),

//           // Horizontal Scroll
//           SizedBox(
//             height: 160,
//             child: ListView.separated(
//               scrollDirection: Axis.horizontal,
//               padding: const EdgeInsets.symmetric(
//                 horizontal: 0,
//               ), // No edge padding
//               itemCount: subjects.length,
//               separatorBuilder: (_, __) => SizedBox(width: 12),
//               itemBuilder: (context, index) {
//                 return Padding(
//                   padding: EdgeInsets.only(
//                     left:
//                         index == 0
//                             ? 12
//                             : 0, // Add left margin only to first item
//                     right:
//                         index == subjects.length - 1
//                             ? 12
//                             : 0, // Add right margin only to last item
//                   ),
//                   child: GestureDetector(
//                     onTap: () async {
//                       final qbankViewModel = Get.find<QbankViewModel>();
//                       final subject = subjects[index];
//                       if (subject.hasSubtopic) {
//                         final subtopics = await qbankViewModel
//                             .fetchSubjectsBySubtopic(subject.id);
//                         Get.toNamed(
//                           AppRoutes.subtopic,
//                           arguments: {
//                             'subtopics': subtopics,
//                             'subject': subject,
//                           },
//                         );
//                         return;
//                       } else {
//                         Get.toNamed(AppRoutes.quiz, arguments: subject);
//                       }
//                     },
//                     child: QbankCard(subject: subjects[index]),
//                   ),
//                 );
//               },
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
