import 'package:flutter/material.dart';
import 'package:get/route_manager.dart';

class ReusableAppbar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  const ReusableAppbar({super.key, required this.title});
  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Get.theme.scaffoldBackgroundColor,
      title: Text(
        title,
        style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
      ),
      centerTitle: false,
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(1),
        child: Divider(
          height: 1,
          thickness: 1,
          color: Colors.grey.withAlpha(40),
        ),
      ),
    );
  }
}
