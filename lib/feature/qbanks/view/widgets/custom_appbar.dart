import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qbank_bd/feature/auth/model/user_model.dart';
import 'package:qbank_bd/core/theme/theme_controller.dart';
import 'package:qbank_bd/core/utils/pallete.dart';
import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';
import 'package:qbank_bd/core/common/widgets/circular_profile_image.dart';

class CustomAppbar extends StatelessWidget implements PreferredSizeWidget {
  CustomAppbar({super.key});

  final authController = Get.find<AuthController>();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = Pallete.isDarkMode(context);
    final themeController = Get.find<ThemeController>();

    return AppBar(
      backgroundColor: theme.scaffoldBackgroundColor,
      centerTitle: false,
      title: Text(
        'Qbank BD',
        style: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          //color: Get.theme.colorScheme.onPrimary,
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(
            isDark ? CupertinoIcons.sun_max_fill : CupertinoIcons.moon_fill,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () {
            final isCurrentlyDark =
                themeController.themeMode.value == ThemeMode.dark ||
                (themeController.themeMode.value == ThemeMode.system &&
                    theme.brightness == Brightness.dark);
            themeController.setThemeMode(
              isCurrentlyDark ? ThemeMode.light : ThemeMode.dark,
            );
          },
        ),
        Obx(() {
          final user = authController.currentUser.value ?? UserModel.empty();
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.red.withAlpha(30),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              children: [
                const Image(
                  image: AssetImage('assets/images/fire_icon.png'),
                  height: 20,
                ),
                const SizedBox(width: 4),
                Text(
                  '${user.streak}',
                  style: const TextStyle(
                    color: Colors.redAccent,
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
          );
        }),
        Obx(() {
          final user = authController.currentUser.value ?? UserModel.empty();
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 14),
            child: CircularProfileImage(
              imageUrl: user.profileImage,
              radius: 16,
              onTap: () {
                Get.toNamed('/profile');
              },
            ),
          );
        }),
      ],
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(1),
        child: Divider(
          height: 1,
          thickness: 1.2,
          color: Colors.grey.withAlpha(40),
        ),
      ),
    );
  }
}
