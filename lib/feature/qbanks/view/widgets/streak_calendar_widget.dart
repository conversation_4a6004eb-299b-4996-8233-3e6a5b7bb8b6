// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:intl/intl.dart';
// import 'package:qbank_bd/core/utils/pallete.dart';
// import 'package:qbank_bd/feature/home/<USER>/profile_viewmodel.dart';

// class StreakCalendarWidget extends StatelessWidget {
//   final ProfileViewModel viewModel;

//   const StreakCalendarWidget({Key? key, required this.viewModel})
//     : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Obx(() => _buildDuolingoStyleCalendar());
//   }

//   Widget _buildDuolingoStyleCalendar() {
//     // Get current month and year from the display month
//     final currentMonth = viewModel.currentDisplayMonth.value.month;
//     final currentYear = viewModel.currentDisplayMonth.value.year;

//     // Create a DateTime for the first day of the month
//     final firstDayOfMonth = DateTime(currentYear, currentMonth, 1);

//     // Get the month name and year
//     final monthName = DateFormat('MMMM yyyy').format(firstDayOfMonth);

//     // Calculate days in month
//     final daysInMonth = DateTime(currentYear, currentMonth + 1, 0).day;

//     // Calculate the weekday of the first day (0 = Sunday, 1 = Monday, etc.)
//     final firstWeekday = firstDayOfMonth.weekday;

//     // Adjust for Sunday start (if first day is Sunday, we want 0, not 7)
//     final firstDayOffset = firstWeekday % 7;

//     return Container(
//       padding: EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Get.theme.colorScheme.surface,
//         borderRadius: BorderRadius.circular(12),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withAlpha(10),
//             blurRadius: 10,
//             offset: Offset(0, 4),
//           ),
//         ],
//       ),
//       child: Column(
//         children: [
//           // Month navigation
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               IconButton(
//                 icon: Icon(Icons.chevron_left),
//                 onPressed: viewModel.previousMonth,
//                 color: Get.theme.colorScheme.onSurface,
//               ),
//               Text(
//                 monthName,
//                 style: TextStyle(
//                   fontSize: 16,
//                   fontWeight: FontWeight.bold,
//                   color: Get.theme.colorScheme.onSurface,
//                 ),
//               ),
//               IconButton(
//                 icon: Icon(Icons.chevron_right),
//                 onPressed: viewModel.nextMonth,
//                 color: Get.theme.colorScheme.onSurface,
//               ),
//             ],
//           ),

//           // Weekday headers
//           Padding(
//             padding: const EdgeInsets.symmetric(vertical: 8.0),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceAround,
//               children: [
//                 _weekdayHeader('S'),
//                 _weekdayHeader('M'),
//                 _weekdayHeader('T'),
//                 _weekdayHeader('W'),
//                 _weekdayHeader('T'),
//                 _weekdayHeader('F'),
//                 _weekdayHeader('S'),
//               ],
//             ),
//           ),

//           // Calendar grid
//           GridView.builder(
//             shrinkWrap: true,
//             physics: NeverScrollableScrollPhysics(),
//             gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//               crossAxisCount: 7,
//               childAspectRatio: 1,
//             ),
//             itemCount: firstDayOffset + daysInMonth,
//             itemBuilder: (context, index) {
//               // Empty cells before the first day of month
//               if (index < firstDayOffset) {
//                 return Container();
//               }

//               // Day cells
//               final day = index - firstDayOffset + 1;
//               final date = DateTime(currentYear, currentMonth, day);

//               // Check if this date is in our active days list
//               final isActive = viewModel.isActiveDay(date);
//               final isToday = viewModel.isToday(date);

//               return _duolingoStyleDayCell(day, isActive, isToday);
//             },
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _weekdayHeader(String text) {
//     return SizedBox(
//       width: 30,
//       child: Text(
//         text,
//         textAlign: TextAlign.center,
//         style: TextStyle(
//           color: Colors.grey,
//           fontWeight: FontWeight.bold,
//           fontSize: 12,
//         ),
//       ),
//     );
//   }

//   Widget _duolingoStyleDayCell(int day, bool isActive, bool isToday) {
//     // Add debug print to check values
//     final date = DateTime(
//       viewModel.currentDisplayMonth.value.year,
//       viewModel.currentDisplayMonth.value.month,
//       day,
//     );
//     print('Day $day: isActive=$isActive, isToday=$isToday, date=$date');

//     return Container(
//       margin: EdgeInsets.all(4),
//       decoration: BoxDecoration(
//         color: isActive ? Pallete.primary.withAlpha(80) : Colors.transparent,
//         borderRadius: BorderRadius.circular(100),
//         border:
//             isToday
//                 ? Border.all(
//                   color: isActive ? Pallete.primary : Colors.grey.shade400,
//                   width: 2,
//                 )
//                 : null,
//       ),
//       child: Center(
//         child: Text(
//           day.toString(),
//           style: TextStyle(
//             color: isActive ? Colors.white : Get.theme.colorScheme.onSurface,
//             fontWeight:
//                 isActive || isToday ? FontWeight.bold : FontWeight.normal,
//             fontSize: 13,
//           ),
//         ),
//       ),
//     );
//   }
// }
