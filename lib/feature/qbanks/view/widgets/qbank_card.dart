import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qbank_bd/core/common/model/qbank_model.dart';
import 'package:qbank_bd/core/router/app_router.dart';
import 'package:qbank_bd/feature/qbanks/viewmodel/quiz_viewmodel.dart';
import 'package:shimmer/shimmer.dart';

class QbankCard extends StatelessWidget {
  final QbankModel subject;

  const QbankCard({super.key, required this.subject});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: ClipRRect(
        borderRadius: BorderRadius.all(Radius.circular(12)),
        child: CachedNetworkImage(
          imageUrl: subject.coverImage,
          fit: BoxFit.cover,
          fadeInDuration: Duration.zero,
          fadeOutDuration: Duration.zero,
          placeholder:
              (context, url) => Shimmer.fromColors(
                baseColor: Colors.grey.shade300,
                highlightColor: Colors.grey.shade100,
                child: Container(color: Colors.white),
              ),
          errorWidget: (context, url, error) => const Icon(Icons.error),
        ),
      ),
    );
  }
}
