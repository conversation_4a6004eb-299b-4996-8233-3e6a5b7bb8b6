// import 'package:flutter/material.dart';
// import 'package:font_awesome_flutter/font_awesome_flutter.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/feature/qbanks/view/widgets/streak_calendar_widget.dart';
// import 'package:qbank_bd/feature/qbanks/view/widgets/user_detail_widget.dart';
// import 'package:qbank_bd/feature/home/<USER>/profile_viewmodel.dart';
// import 'package:qbank_bd/core/common/widgets/circular_profile_image.dart';

// class ProfilePage extends StatefulWidget {
//   const ProfilePage({super.key});

//   @override
//   State<ProfilePage> createState() => _ProfilePageState();
// }

// class _ProfilePageState extends State<ProfilePage> {
//   // Initialize the ViewModel
//   final ProfileViewModel _viewModel = Get.put(ProfileViewModel());

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: Get.theme.scaffoldBackgroundColor,
//       appBar: AppBar(
//         title: Text(
//           'Profile',
//           style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
//         ),
//         centerTitle: true,
//       ),
//       body: Obx(() {
//         final user = _viewModel.authController.currentUser.value;
//         if (user == null) {
//           return Center(child: CircularProgressIndicator());
//         }
//         return SingleChildScrollView(
//           padding: const EdgeInsets.all(16),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.center,
//             children: [
//               // Profile Info
//               CircularProfileImage(imageUrl: user.profileImage, radius: 50),
//               SizedBox(height: 8),
//               Text(
//                 user.name,
//                 style: TextStyle(
//                   fontSize: 22,
//                   fontWeight: FontWeight.bold,
//                   color: Get.theme.colorScheme.onSurface,
//                 ),
//               ),
//               SizedBox(height: 16),
//               Divider(color: Colors.grey.withAlpha(50), thickness: 1),
//               SizedBox(height: 16),
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   UserDetailWidget(
//                     title: 'Points',
//                     value: '${user.points}',
//                     icon: FontAwesomeIcons.bolt,
//                     color: Colors.orange,
//                   ),
//                   SizedBox(width: 14), // Increased from 12 to 16
//                   UserDetailWidget(
//                     title: 'Streak',
//                     value: '${user.streak}',
//                     icon: FontAwesomeIcons.fire,
//                     color: Colors.red,
//                   ),
//                   SizedBox(width: 14), // Increased from 12 to 16
//                   UserDetailWidget(
//                     title: 'Exams',
//                     value: '${user.exams}',
//                     icon: FontAwesomeIcons.solidFileLines,
//                     color: Colors.green,
//                   ),
//                 ],
//               ),
//               SizedBox(height: 16),
//               Divider(color: Colors.grey.withAlpha(50), thickness: 1),

//               // Streak Calendar Section
//               SizedBox(height: 24),
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Text(
//                     'Your Streak Calendar',
//                     style: TextStyle(
//                       fontSize: 18,
//                       fontWeight: FontWeight.bold,
//                       color: Get.theme.colorScheme.onSurface,
//                     ),
//                   ),
//                   Row(
//                     children: [
//                       Icon(FontAwesomeIcons.fire, color: Colors.red, size: 20),
//                       SizedBox(width: 4),
//                       Text(
//                         '${user.streak} days',
//                         style: TextStyle(
//                           color: Colors.red,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ],
//               ),
//               SizedBox(height: 16),
//               StreakCalendarWidget(viewModel: _viewModel),
//             ],
//           ),
//         );
//       }),
//     );
//   }
// }
