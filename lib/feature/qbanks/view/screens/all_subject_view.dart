import 'package:flutter/material.dart';
import 'package:qbank_bd/core/common/model/qbank_model.dart';
import 'package:qbank_bd/feature/qbanks/view/widgets/qbank_card.dart';

class AllSubjectView extends StatelessWidget {
  final List<QbankModel> subjects;

  const AllSubjectView({super.key, required this.subjects});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: const BackButton(),
        title: const Text(
          'All Subjects',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      ),
      body:
          subjects.isEmpty
              ? const Center(
                child: Text(
                  'No subjects available',
                  style: TextStyle(fontSize: 16),
                ),
              )
              : Padding(
                padding: const EdgeInsets.all(14),
                child: GridView.builder(
                  itemCount: subjects.length,
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 14,
                    mainAxisSpacing: 14,
                    childAspectRatio: 1,
                  ),
                  itemBuilder: (_, index) {
                    return QbankCard(subject: subjects[index]);
                  },
                ),
              ),
    );
  }
}
