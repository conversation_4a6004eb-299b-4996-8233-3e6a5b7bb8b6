// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/feature/qbanks/view/widgets/reusable_appbar.dart';
// import 'package:qbank_bd/feature/qbanks/view/widgets/section_widget.dart';
// import 'package:qbank_bd/feature/qbanks/view/widgets/section_shimmer.dart';
// import 'package:qbank_bd/feature/qbanks/viewmodel/qbank_viewmodel.dart';

// class QbankScreen extends StatefulWidget {
//   const QbankScreen({super.key});

//   @override
//   State<QbankScreen> createState() => _QbankScreenState();
// }

// class _QbankScreenState extends State<QbankScreen> {
//   final _qbankViewmodel = Get.put(QbankViewModel());

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: ReusableAppbar(title: 'Qbanks'),
//       body: Obx(() {
//         // Show shimmer only if data is still loading
//         if (_qbankViewmodel.isLoading.value) {
//           return _buildShimmerLoading();
//         } else {
//           return SafeArea(
//             child: SingleChildScrollView(
//               padding: EdgeInsets.symmetric(vertical: 12),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children:
//                     _qbankViewmodel.categories.map((categoryName) {
//                       final categorySubjects =
//                           _qbankViewmodel.subjects
//                               .where((s) => s.category == categoryName)
//                               .toList();

//                       if (categorySubjects.isEmpty) return SizedBox.shrink();

//                       return SectionWidget(
//                         title: categoryName,
//                         subjects: categorySubjects,
//                       );
//                     }).toList(),
//               ),
//             ),
//           );
//         }
//       }),
//     );
//   }

//   Widget _buildShimmerLoading() {
//     return SafeArea(
//       child: SingleChildScrollView(
//         padding: EdgeInsets.symmetric(vertical: 12),
//         child: Column(
//           children: List.generate(
//             3, // Show 3 shimmer section placeholders
//             (index) => const SectionShimmer(),
//           ),
//         ),
//       ),
//     );
//   }
// }
