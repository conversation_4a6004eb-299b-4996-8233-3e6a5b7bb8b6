// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:qbank_bd/core/common/model/qbank_model.dart';
// import 'package:qbank_bd/core/router/app_router.dart';
// import 'package:qbank_bd/core/utils/utils.dart';
// import 'package:qbank_bd/feature/qbanks/viewmodel/quiz_viewmodel.dart';
// import 'package:qbank_bd/feature/qbanks/view/widgets/quiz_card.dart';
// import 'package:qbank_bd/feature/qbanks/view/widgets/quiz_card_shimmer.dart';
// import 'package:qbank_bd/feature/question/viewmodel/question_viewmodel.dart';
// import 'package:qbank_bd/feature/settings/viewmodel/downloads_viewmodel.dart';

// class QuizScreen extends StatefulWidget {
//   final QbankModel subject;
//   const QuizScreen({super.key, required this.subject});

//   @override
//   State<QuizScreen> createState() => _QuizScreenState();
// }

// class _QuizScreenState extends State<QuizScreen> {
//   late final QuizViewModel quizViewModel;
//   final ScrollController _scrollController = ScrollController();

//   @override
//   void initState() {
//     super.initState();

//     quizViewModel = Get.put(QuizViewModel(subtopicId: widget.subject.id));

//     _scrollController.addListener(_onScroll);
//   }

//   void _onScroll() {
//     if (_scrollController.position.pixels >=
//         _scrollController.position.maxScrollExtent - 200) {
//       quizViewModel.loadMoreQuizzes();
//     }
//   }

//   @override
//   void dispose() {
//     _scrollController.removeListener(_onScroll);
//     _scrollController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text(
//           widget.subject.name,
//           style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
//         ),
//         bottom: PreferredSize(
//           preferredSize: const Size.fromHeight(1),
//           child: Divider(
//             height: 1,
//             thickness: 1,
//             color: Colors.grey.withAlpha(40),
//           ),
//         ),
//       ),
//       body: Obx(() {
//         if (quizViewModel.isLoading.value) {
//           return _buildShimmerLoading();
//         } else {
//           return _buildQuizList();
//         }
//       }),
//     );
//   }

//   Widget _buildQuizList() {
//     return Obx(
//       () => ListView.builder(
//         controller: _scrollController,
//         padding: const EdgeInsets.fromLTRB(14, 14, 14, 0),
//         itemCount:
//             quizViewModel.quizzes.length + (quizViewModel.hasMoreData ? 1 : 0),
//         itemBuilder: (context, index) {
//           if (index == quizViewModel.quizzes.length) {
//             return Obx(() {
//               return quizViewModel.isLoadingMore.value
//                   ? const Padding(
//                     padding: EdgeInsets.all(8.0),
//                     child: Center(child: CircularProgressIndicator()),
//                   )
//                   : const SizedBox();
//             });
//           }

//           final quiz = quizViewModel.quizzes[index];
//           print(quiz.id);
//           return Padding(
//             padding: const EdgeInsets.only(bottom: 14),
//             child: GestureDetector(
//               onTap: () {
//                 showAlertDialog(
//                   quiz.name,
//                   'This exam contains ${quiz.questionCount} questions and time allowed ${quiz.duration} minutes. Are you ready for the exam?',
//                   isYesDestructive: false,
//                   () {
//                     Get.back();
//                   },
//                   () {
//                     Get.back();
//                     final questionViewModel = Get.put(QuestionViewModel());
//                     questionViewModel.fetchQuestions(quiz.id);
//                     Get.toNamed(
//                       AppRoutes.question,
//                       arguments: {'subjectId': widget.subject.id, 'quiz': quiz},
//                     );
//                   },
//                 );
//               },
//               child: FutureBuilder<bool>(
//                 future: quizViewModel.isQuizDownloaded(quiz.id),
//                 builder: (context, snapshot) {
//                   final isDownloaded = snapshot.data ?? false;
//                   print(quizViewModel.userQuizzes);
//                   return QuizCard(
//                     quiz: quiz,
//                     isCompleted: quizViewModel.userQuizzes.contains(quiz.id),
//                     isDownloaded: isDownloaded,
//                     isDownloading: quizViewModel.isDownloadingQuiz(quiz.id),
//                     onDownload: () async {
//                       final isDownloaded = await quizViewModel.isQuizDownloaded(
//                         quiz.id,
//                       );

//                       if (isDownloaded) {
//                         final viewModel = Get.put(DownloadsViewModel());
//                         viewModel.deleteDownloadedQuiz(quiz.id);
//                         return;
//                       }

//                       final success = await quizViewModel.downloadQuiz(quiz.id);
//                       if (success) {
//                         showCustomSnackbar(
//                           title: 'Download Complete',
//                           message: 'Quiz has been downloaded for offline use.',
//                           isSuccess: true,
//                         );
//                       } else {
//                         showCustomSnackbar(
//                           title: 'Download Failed',
//                           message: 'Failed to download quiz. Please try again.',
//                           isSuccess: false,
//                         );
//                       }
//                     },
//                   );
//                 },
//               ),
//             ),
//           );
//         },
//       ),
//     );
//   }

//   Widget _buildShimmerLoading() {
//     return ListView.builder(
//       padding: const EdgeInsets.fromLTRB(14, 14, 14, 0),
//       itemCount: 8,
//       itemBuilder:
//           (context, index) => const Padding(
//             padding: EdgeInsets.only(bottom: 14),
//             child: QuizCardShimmer(),
//           ),
//     );
//   }
// }
