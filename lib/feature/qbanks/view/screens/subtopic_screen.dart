import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qbank_bd/core/common/model/qbank_model.dart';
import 'package:qbank_bd/core/router/app_router.dart';
import 'package:qbank_bd/feature/qbanks/view/widgets/qbank_card.dart';

class SubtopicScreen extends StatelessWidget {
  final List<QbankModel> subtopics;
  final QbankModel subject;

  const SubtopicScreen({
    super.key,
    required this.subtopics,
    required this.subject,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: const BackButton(),
        title: Text(
          subject.name,
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      ),
      body:
          subtopics.isEmpty
              ? const Center(
                child: Text(
                  'No subjects available',
                  style: TextStyle(fontSize: 16),
                ),
              )
              : Padding(
                padding: const EdgeInsets.all(14),
                child: GridView.builder(
                  itemCount: subtopics.length,
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 14,
                    mainAxisSpacing: 14,
                    childAspectRatio: 1,
                  ),
                  itemBuilder: (_, index) {
                    final subtopic = subtopics[index];
                    return GestureDetector(
                      onTap: () {
                        Get.toNamed(AppRoutes.quiz, arguments: subtopic);
                      },
                      child: QbankCard(subject: subtopic),
                    );
                  },
                ),
              ),
    );
  }
}
