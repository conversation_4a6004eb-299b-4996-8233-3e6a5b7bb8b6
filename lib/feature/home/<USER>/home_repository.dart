// lib/data/repositories/subject_repository.dart

import 'package:supabase_flutter/supabase_flutter.dart';

class SubjectRepository {
  final supabase = Supabase.instance.client;

  Future<List<Map<String, dynamic>>> fetchSubjectsWithProgress({
    required String levelId,
    required String userId,
  }) async {
    try {
      final result = await Supabase.instance.client.rpc(
        'get_subjects_with_progress',
        params: {'level_id': levelId, 'uid': userId},
      );

      return List<Map<String, dynamic>>.from(result);
    } catch (e) {
      throw Exception('RPC call failed: $e');
    }
  }
}
