// import 'package:flutter/material.dart';
// import 'package:font_awesome_flutter/font_awesome_flutter.dart';
// import 'package:qbank_bd/feature/home/<USER>/widgets/subject_card.dart';
// import 'package:qbank_bd/feature/home/<USER>/home_viewmodel.dart';
// import 'package:qbank_bd/feature/qbanks/view/widgets/custom_appbar.dart';
// import 'package:get/get.dart';

// class PracticeScreen extends StatefulWidget {
//   const PracticeScreen({super.key});

//   @override
//   State<PracticeScreen> createState() => _PracticeScreenState();
// }

// class _PracticeScreenState extends State<PracticeScreen> {
//   final SubjectController _subjectController = Get.put(SubjectController());

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: CustomAppbar(),
//       body: Column(
//         children: [
//           Expanded(
//             child: Obx(() {
//               if (_subjectController.isLoading.value) {
//                 return const Center(child: CircularProgressIndicator());
//               }

//               if (_subjectController.subjects.isEmpty) {
//                 return const Center(child: Text("No subjects found"));
//               }

//               return ListView.builder(
//                 padding: const EdgeInsets.symmetric(vertical: 16),
//                 itemCount: _subjectController.subjects.length,
//                 itemBuilder: (context, index) {
//                   final subject = _subjectController.subjects[index];
//                   return PracticeSubjectCard(
//                     name: subject.name,
//                     icon: _getFontAwesomeIcon(subject.icon),
//                     color: HexColor.fromHex(subject.color),
//                     totalQuestions: subject.questionCount,
//                     completedQuestions: subject.done,
//                     onTap: (value) {
//                       // Your tap logic
//                     },
//                   );
//                 },
//               );
//             }),
//           ),
//         ],
//       ),
//     );
//   }

//   /// Optional: Converts icon name to FontAwesomeIcon
//   IconData _getFontAwesomeIcon(String iconName) {
//     switch (iconName) {
//       case 'fa-atom':
//         return FontAwesomeIcons.atom;
//       case 'fa-vial':
//         return FontAwesomeIcons.vial;
//       case 'fa-dna':
//         return FontAwesomeIcons.dna;
//       case 'fa-calculator':
//         return FontAwesomeIcons.calculator;
//       case 'fa-book-open':
//         return FontAwesomeIcons.bookOpen;
//       case 'fa-language':
//         return FontAwesomeIcons.language;
//       case 'fa-computer':
//         return FontAwesomeIcons.computer;
//       case 'fa-chart-line':
//         return FontAwesomeIcons.chartLine;
//       case 'fa-briefcase':
//         return FontAwesomeIcons.briefcase;
//       case 'fa-file-invoice-dollar':
//         return FontAwesomeIcons.fileInvoiceDollar;
//       case 'fa-landmark':
//         return FontAwesomeIcons.landmark;
//       case 'fa-globe':
//         return FontAwesomeIcons.globe;
//       case 'fa-people-group':
//         return FontAwesomeIcons.peopleGroup;
//       case 'fa-brain':
//         return FontAwesomeIcons.brain;
//       case 'fa-lightbulb':
//         return FontAwesomeIcons.lightbulb;
//       default:
//         return FontAwesomeIcons.question; // fallback
//     }
//   }
// }

// /// Utility to convert hex to Color
// class HexColor extends Color {
//   HexColor(super.hex);

//   factory HexColor.fromHex(String hexString) {
//     final buffer = StringBuffer();
//     if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
//     buffer.write(hexString.replaceFirst('#', ''));
//     return HexColor(int.parse(buffer.toString(), radix: 16));
//   }
// }
