// // lib/controllers/subject_controller.dart

// import 'package:get/get.dart';
// import 'package:qbank_bd/feature/auth/model/user_model.dart';
// import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';
// import 'package:qbank_bd/feature/home/<USER>/subject_model.dart';
// import 'package:qbank_bd/feature/home/<USER>/home_repository.dart';

// class SubjectController extends GetxController {
//   final SubjectRepository _repository = SubjectRepository();

//   RxList<SubjectModel> subjects = <SubjectModel>[].obs;
//   RxBool isLoading = false.obs;
//   final _userController = Get.find<AuthController>();
//   UserModel get user => _userController.currentUser.value ?? UserModel.empty();
//   @override
//   void onInit() {
//     super.onInit();
//     loadSubjects();
//   }

//   Future<void> loadSubjects() async {
//     isLoading.value = true;

//     try {
//       final data = await _repository.fetchSubjectsWithProgress(
//         levelId: user.level,
//         userId: user.id,
//       );
//       subjects.value = data.map((e) => SubjectModel.fromJson(e)).toList();
//     } catch (e) {
//       print('Error loading subjects: $e');
//       Get.snackbar('Error', e.toString());
//     } finally {
//       isLoading.value = false;
//     }
//   }
// }
