// import 'package:get/get.dart';
// import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';

// class ProfileViewModel extends GetxController {
//   // Current date for reference
//   final DateTime today = DateTime.now();

//   // Current displayed month and year
//   final Rx<DateTime> currentDisplayMonth = DateTime.now().obs;

//   // Auth controller to get user data
//   final AuthController authController = Get.find<AuthController>();

//   // Track if we've loaded activity data
//   final RxBool hasLoadedActivityData = false.obs;

//   // Store active days
//   final RxList<DateTime> activeDays = <DateTime>[].obs;

//   @override
//   void onInit() {
//     super.onInit();
//     currentDisplayMonth.value = DateTime(today.year, today.month, 1);
//     loadActivityData();
//   }

//   Future<void> loadActivityData() async {
//     if (hasLoadedActivityData.value) return;

//     final user = authController.currentUser.value;
//     if (user == null) return;

//     // Clear existing active days
//     activeDays.clear();

//     // If user has a last quiz date
//     if (user.lastQuizDate != null) {
//       final lastQuizDate = user.lastQuizDate!;

//       // Create a DateTime that represents just the date portion (year, month, day)
//       // This ensures consistent date comparison
//       final lastActiveDay = DateTime(
//         lastQuizDate.year,
//         lastQuizDate.month,
//         lastQuizDate.day,
//       );

//       // Always add the last active day to show when the user last practiced
//       activeDays.add(lastActiveDay);
//       print('Added last active day: $lastActiveDay'); // Debug print

//       // Add previous consecutive days based on streak (if streak > 1)
//       if (user.streak > 1) {
//         for (int i = 1; i < user.streak; i++) {
//           final previousDay = DateTime(
//             lastActiveDay.year,
//             lastActiveDay.month,
//             lastActiveDay.day - i,
//           );
//           activeDays.add(previousDay);
//           print('Added streak day: $previousDay'); // Debug print
//         }
//       }
//     }

//     // Force UI update
//     activeDays.refresh();
//     hasLoadedActivityData.value = true;
//     print('Active days loaded: ${activeDays.length}'); // Debug print
//   }

//   void previousMonth() {
//     currentDisplayMonth.value = DateTime(
//       currentDisplayMonth.value.year,
//       currentDisplayMonth.value.month - 1,
//       1,
//     );
//   }

//   void nextMonth() {
//     currentDisplayMonth.value = DateTime(
//       currentDisplayMonth.value.year,
//       currentDisplayMonth.value.month + 1,
//       1,
//     );
//   }

//   bool isActiveDay(DateTime date) {
//     // Normalize the date to just year, month, day for comparison
//     final normalizedDate = DateTime(date.year, date.month, date.day);

//     return activeDays.any((activeDay) {
//       // Normalize active day as well
//       final normalizedActiveDay = DateTime(
//         activeDay.year,
//         activeDay.month,
//         activeDay.day,
//       );

//       return normalizedActiveDay.isAtSameMomentAs(normalizedDate);
//     });
//   }

//   bool isToday(DateTime date) {
//     return today.year == date.year &&
//         today.month == date.month &&
//         today.day == date.day;
//   }
// }
