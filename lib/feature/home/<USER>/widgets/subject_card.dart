import 'package:flutter/material.dart';

class PracticeSubjectCard extends StatefulWidget {
  final String name;
  final IconData icon;
  final Color color;
  final int totalQuestions;
  final int completedQuestions;
  final Function(bool?) onTap;

  const PracticeSubjectCard({
    super.key,
    required this.name,
    required this.icon,
    required this.color,
    required this.totalQuestions,
    required this.completedQuestions,
    required this.onTap,
  });

  @override
  State<PracticeSubjectCard> createState() => _PracticeSubjectCardState();
}

class _PracticeSubjectCardState extends State<PracticeSubjectCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _progressAnimation;

  double get progress =>
      widget.totalQuestions == 0
          ? 0.0
          : widget.completedQuestions / widget.totalQuestions;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _progressAnimation = Tween<double>(
      begin: 0,
      end: progress,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 14, vertical: 6),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme),
          const SizedBox(height: 12),
          _buildProgressBar(theme),
        ],
      ),
    );
  }

  Widget _buildHeader(ColorScheme theme) {
    return Row(
      children: [
        // Subject icon
        Container(
          height: 48,
          width: 48,
          decoration: BoxDecoration(
            color: widget.color.withAlpha(40),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(widget.icon, color: widget.color),
        ),

        const SizedBox(width: 16),

        // Subject info
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.name,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: theme.onSurface,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '${widget.completedQuestions} of ${widget.totalQuestions} Completed',
                style: TextStyle(
                  fontSize: 14,
                  color: theme.onSurface.withAlpha(160),
                ),
              ),
            ],
          ),
        ),

        // Arrow icon
        Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: theme.onSurface.withAlpha(100),
        ),
      ],
    );
  }

  Widget _buildProgressBar(ColorScheme theme) {
    return Row(
      children: [
        // Progress bar
        Expanded(
          child: AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  height: 6,
                  color: theme.onSurface.withAlpha(20),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: FractionallySizedBox(
                      widthFactor: _progressAnimation.value.clamp(0, 1),
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              widget.color.withAlpha(120),
                              widget.color.withAlpha(220),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        const SizedBox(width: 12),

        // Percentage text
        AnimatedBuilder(
          animation: _progressAnimation,
          builder: (context, child) {
            final percentage = (_progressAnimation.value * 100).toInt();
            return Text(
              '$percentage%',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            );
          },
        ),
      ],
    );
  }
}
