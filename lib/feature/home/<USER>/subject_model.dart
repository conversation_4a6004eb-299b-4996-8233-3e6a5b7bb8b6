class SubjectModel {
  final int id;
  final String name;
  final int questionCount;
  final String color;
  final String icon;
  final int done;
  final int correct;
  final int wrong;
  final int skipped;

  SubjectModel({
    required this.id,
    required this.name,
    required this.questionCount,
    required this.color,
    required this.icon,
    required this.done,
    required this.correct,
    required this.wrong,
    required this.skipped,
  });

  factory SubjectModel.fromJson(Map<String, dynamic> json) {
    return SubjectModel(
      id: json['id'],
      name: json['name'],
      questionCount: json['question_count'],
      color: json['color'],
      icon: json['icon'],
      done: json['done'],
      correct: json['correct'],
      wrong: json['wrong'],
      skipped: json['skipped'],
    );
  }
}
