import 'package:qbank_bd/core/services/supabase_service.dart';
import 'package:qbank_bd/core/utils/utils.dart';
import 'package:qbank_bd/feature/auth/model/user_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:google_sign_in/google_sign_in.dart';

class AuthRepository {
  final supabase = SupabaseService().client;

  Future<bool> signInWithGoogle() async {
    try {
      const webClientId =
          '************-jtgflr26i4ut616slllnatm0cdgmos6m.apps.googleusercontent.com';
      const iosClientId =
          '************-0sr9rsseibfl5l4ogehtkldvtm5rvpsj.apps.googleusercontent.com';

      final googleUser =
          await GoogleSignIn(
            clientId: iosClientId,
            serverClientId: webClientId,
          ).signIn();

      final googleAuth = await googleUser!.authentication;

      await supabase.auth.signInWithIdToken(
        provider: OAuthProvider.google,
        idToken: googleAuth.idToken!,
        accessToken: googleAuth.accessToken,
      );

      final user = supabase.auth.currentUser;
      if (user == null) throw Exception('User not logged in');

      final existing =
          await supabase.from('users').select().eq('id', user.id).maybeSingle();

      return existing != null;
    } catch (e) {
      throw Exception('Google Sign-In failed: $e');
    }
  }

  /// Create user profile and default data
  Future<void> createUserProfile({
    required String userId,
    required String name,
    required String email,
    required String? phone,
    required String level,
    required String profileImage,
  }) async {
    final user = UserModel(
      id: userId,
      name: name,
      email: email,
      phone: phone,
      level: intLevel(level),
      profileImage: profileImage,
      totalAura: 0,
      monthAura: 0,
      streak: 0,
      exams: 0,
      createdAt: DateTime.now(),
    );

    await supabase.from('users').insert(user.toMap());
  }
}
