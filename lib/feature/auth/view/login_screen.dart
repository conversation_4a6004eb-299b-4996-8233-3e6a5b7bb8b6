import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qbank_bd/feature/auth/viewmodel/auth_viewmodel.dart';

class LoginScreen extends StatelessWidget {
  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  LoginScreen({super.key});
  final controller = Get.put(AuthViewModel());

  void dispose() {
    Get.delete<AuthViewModel>();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: GestureDetector(
        onTap: () {
          // Dismiss keyboard when tapping outside
          FocusScope.of(context).unfocus();
        },
        child: Safe<PERSON>rea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 48),

                // Login Animation/Image
                Center(
                  child: Image.asset(
                    Get.isDarkMode
                        ? 'assets/images/login_image_dark.gif'
                        : 'assets/images/login_image_light.gif',
                    height: Get.height * 0.5,
                  ),
                ),
                SizedBox(height: Get.height * 0.08),
                // Sign in or sign up with text
                Row(
                  children: [
                    Expanded(
                      child: Divider(
                        color: Colors.grey.shade400,
                        thickness: 0.5,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        'Sign in or Sign up with',
                        style: TextStyle(
                          color: Colors.grey.shade400,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Divider(
                        color: Colors.grey.shade400,
                        thickness: 0.5,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: Get.height * 0.04),

                // Social login buttons in a row
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Google button
                    _buildSocialButton(
                      onTap: () => controller.signInWithGoogle(),
                      icon: Image.asset(
                        'assets/images/google_icon.png',
                        height: 24,
                      ),
                    ),

                    const SizedBox(width: 24),

                    // Facebook button
                    _buildSocialButton(
                      onTap: () {},
                      icon: const Icon(
                        Icons.facebook,
                        color: Colors.blue,
                        size: 28,
                      ),
                    ),

                    // Apple button (iOS only)
                    if (Platform.isIOS) ...[
                      const SizedBox(width: 24),
                      _buildSocialButton(
                        onTap: () {},
                        icon: Icon(Icons.apple, size: 28),
                      ),
                    ],
                  ],
                ),

                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSocialButton({
    required VoidCallback onTap,
    required Widget icon,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(30),
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Get.theme.colorScheme.surface,
        ),
        child: Center(child: icon),
      ),
    );
  }
}
