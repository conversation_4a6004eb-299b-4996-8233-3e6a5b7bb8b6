import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:get/instance_manager.dart';
import 'package:get/route_manager.dart';
import 'package:qbank_bd/core/common/widgets/q_buttons.dart';
import 'package:qbank_bd/core/common/widgets/textfield.dart';
import 'package:qbank_bd/core/utils/utils.dart';
import 'package:qbank_bd/feature/auth/viewmodel/auth_viewmodel.dart';

class ProfileSetupScreen extends StatefulWidget {
  const ProfileSetupScreen({super.key});

  @override
  State<ProfileSetupScreen> createState() => _ProfileSetupScreenState();
}

class _ProfileSetupScreenState extends State<ProfileSetupScreen> {
  final AuthViewModel authViewModel = Get.find<AuthViewModel>();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();

  String selectedItem = 'HSC Science';

  @override
  void initState() {
    super.initState();
    // Pre-fill with Google data if available
    nameController.text = authViewModel.googleUserName.value;
    phoneController.text = authViewModel.googleUserPhone.value;
  }

  @override
  void dispose() {
    nameController.dispose();
    phoneController.dispose();
    super.dispose();
  }

  void _submitProfile() async {
    // Validate inputs
    if (!authViewModel.validateName(nameController.text)) {
      return;
    }

    // Format phone number if provided
    final phone = authViewModel.formatPhoneNumber(phoneController.text);

    // Submit profile with either selected image or default avatar
    await authViewModel.createUserProfile(
      name: nameController.text.trim(),
      phone: phone.isNotEmpty ? phone : null,
      level: availableLevels[availableLevels.indexOf(selectedItem)],
    );
  }

  void _showCupertinoActionSheet(BuildContext context) {
    showCupertinoLevelSelector(
      context: context,
      currentLevel: selectedItem,
      onLevelSelected: (item) {
        setState(() => selectedItem = item);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Get.theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Column(
            children: [
              const Spacer(flex: 1),
              SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    /// Name TextField
                    QTextField(hintText: 'Name', controller: nameController),

                    const SizedBox(height: 16),

                    /// Phone Number TextField with always visible prefix
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 16,
                          ),
                          decoration: BoxDecoration(
                            color: Get.theme.colorScheme.surface,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '+88',
                            style: TextStyle(
                              color: Get.theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: QTextField(
                            hintText: 'Phone Number(optional)',
                            controller: phoneController,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    /// iOS-style context menu
                    GestureDetector(
                      onTap: () => _showCupertinoActionSheet(context),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 18,
                        ),
                        decoration: BoxDecoration(
                          color: Get.theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                selectedItem,
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                            const Icon(
                              CupertinoIcons.chevron_down,
                              color: Colors.white,
                              size: 16,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    /// Next Button
                    Obx(
                      () => QExpandedButton(
                        text: 'Next',
                        onPressed:
                            authViewModel.isLoading.value
                                ? null
                                : _submitProfile,
                        isLoading: authViewModel.isLoading.value,
                      ),
                    ),

                    Obx(
                      () =>
                          authViewModel.errorMessage.value.isNotEmpty
                              ? Padding(
                                padding: const EdgeInsets.only(top: 16),
                                child: Text(
                                  authViewModel.errorMessage.value,
                                  style: const TextStyle(color: Colors.red),
                                  textAlign: TextAlign.center,
                                ),
                              )
                              : const SizedBox.shrink(),
                    ),
                  ],
                ),
              ),
              const Spacer(flex: 2),
            ],
          ),
        ),
      ),
    );
  }
}
