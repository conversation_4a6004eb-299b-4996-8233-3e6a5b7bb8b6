import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';
import 'dart:async';

class ConnectivityController extends GetxController {
  final _connectivity = Connectivity();
  final isOnline = false.obs;
  StreamSubscription<List<ConnectivityResult>>? _sub;

  @override
  void onInit() {
    super.onInit();

    // Initial check
    _connectivity.checkConnectivity().then((result) {
      _updateStatus(result);
    });

    // Listen to changes
    _sub = _connectivity.onConnectivityChanged.listen(_updateStatus);
  }

  void _updateStatus(List<ConnectivityResult> results) {
    // Treat WiFi, mobile, or ethernet as online
    isOnline.value = results.any(
      (result) =>
          result == ConnectivityResult.wifi ||
          result == ConnectivityResult.mobile ||
          result == ConnectivityResult.ethernet,
    );
  }

  @override
  void onClose() {
    _sub?.cancel();
    super.onClose();
  }
}
