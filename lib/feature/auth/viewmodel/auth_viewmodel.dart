import 'package:get/get.dart';
import 'package:qbank_bd/core/router/app_router.dart';
import 'package:qbank_bd/core/utils/utils.dart';
import 'package:qbank_bd/feature/auth/repository/auth_repository.dart';
import 'package:qbank_bd/feature/auth/view/profile_setup_screen.dart';
import 'package:qbank_bd/feature/auth/viewmodel/auth_controller.dart';

class AuthViewModel extends GetxController {
  final _repo = AuthRepository();

  final isLoading = false.obs;
  final errorMessage = ''.obs;

  final googleUserName = ''.obs;
  final googleUserEmail = ''.obs;
  final googleUserPhone = ''.obs;
  final googleUserPhoto = ''.obs;

  Future<void> signInWithGoogle() async {
    isLoading.value = true;
    errorMessage.value = '';
    try {
      final exists = await _repo.signInWithGoogle();
      final user = _repo.supabase.auth.currentUser;

      if (user != null) {
        googleUserName.value = user.userMetadata?['full_name'] ?? '';
        googleUserEmail.value = user.email ?? '';
        googleUserPhone.value = user.phone ?? '';
        googleUserPhoto.value = user.userMetadata?['avatar_url'] ?? '';
      }

      exists
          ? Get.offAllNamed(AppRoutes.menu)
          : Get.to(() => const ProfileSetupScreen());
    } catch (e) {
      showCustomSnackbar(
        title: 'Error',
        message: 'Failed to sign in: $e',
        isSuccess: false,
      );
      errorMessage.value = 'Google Sign-In failed: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> createUserProfile({
    required String name,
    required String? phone,
    required String level,
  }) async {
    isLoading.value = true;
    errorMessage.value = '';
    try {
      final user = _repo.supabase.auth.currentUser;
      if (user == null) throw Exception('User not logged in');

      await _repo.createUserProfile(
        userId: user.id,
        name: name,
        email: googleUserEmail.value,
        phone: phone,
        profileImage: googleUserPhoto.value,
        level: level,
      );

      // Refresh user data to ensure currentUser is populated
      await Get.find<AuthController>().refreshUserData();
      Get.offAllNamed(AppRoutes.menu);
    } catch (e) {
      errorMessage.value = 'Failed to create profile: $e';
    } finally {
      isLoading.value = false;
    }
  }

  bool validateName(String name) {
    if (name.trim().isEmpty) {
      errorMessage.value = 'Please enter your name';
      return false;
    }
    return true;
  }

  String formatPhoneNumber(String phoneNumber) {
    return phoneNumber.trim().isNotEmpty ? '+88${phoneNumber.trim()}' : '';
  }
}
