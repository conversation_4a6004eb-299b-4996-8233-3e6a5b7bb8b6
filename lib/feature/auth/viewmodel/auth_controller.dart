import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:qbank_bd/core/router/app_router.dart';
import 'package:qbank_bd/core/services/supabase_service.dart';
import 'package:qbank_bd/core/utils/utils.dart';
import 'package:qbank_bd/feature/auth/model/user_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:qbank_bd/core/services/database_helper.dart';
import 'package:qbank_bd/core/services/shared_prefs_helper.dart';

class AuthController extends GetxController {
  final supabase = SupabaseService().client;

  final user = Rxn<User>();
  final currentUser = Rxn<UserModel>();
  final isConnected = true.obs;

  final Connectivity _connectivity = Connectivity();

  @override
  void onInit() {
    super.onInit();
    _initConnectivity();
    _initializeAuthFlow();
  }

  Future<void> _initConnectivity() async {
    try {
      // Initial connectivity check
      final initialResult = await _connectivity.checkConnectivity();
      isConnected.value =
          initialResult.isNotEmpty &&
          !initialResult.contains(ConnectivityResult.none);

      // Listen for connectivity changes
      _connectivity.onConnectivityChanged.listen((
        List<ConnectivityResult> results,
      ) {
        isConnected.value =
            results.isNotEmpty && !results.contains(ConnectivityResult.none);
        if (isConnected.value) {
          refreshUserData();
        }
      });
    } catch (e) {
      isConnected.value = false;
      _handleError('Connectivity error', e);
    }
  }

  Future<void> _initializeAuthFlow() async {
    try {
      final loggedIn = await SharedPrefsHelper.isLoggedIn();

      if (loggedIn) {
        final cachedUser = await DatabaseHelper.instance.getUser();
        if (cachedUser != null) {
          currentUser.value = UserModel.fromCache(cachedUser);
        } else {
          final sessionUser = supabase.auth.currentUser;
          user.value = sessionUser;
          if (sessionUser != null) {
            await _loadUserAndCache();
          }
        }
      } else {
        user.value = null;
        currentUser.value = null;
      }

      _listenToAuthChanges();
    } catch (e) {
      user.value = null;
      currentUser.value = null;
      _handleError('Failed to initialize authentication', e);
    }
  }

  void _listenToAuthChanges() {
    supabase.auth.onAuthStateChange.listen((authState) async {
      try {
        final sessionUser = authState.session?.user;
        user.value = sessionUser;

        if (sessionUser == null) {
          currentUser.value = null;
          await SharedPrefsHelper.setLoggedIn(false);
          await DatabaseHelper.instance.deleteUser();
        } else {
          await _loadUserAndCache();
          await SharedPrefsHelper.setLoggedIn(true);
        }
      } catch (e) {
        _handleError('Authentication state change error', e);
      }
    });
  }

  Future<void> _loadUserAndCache() async {
    final uid = user.value?.id;
    if (uid == null || !isConnected.value) return;

    try {
      final data =
          await supabase.from('users').select().eq('id', uid).maybeSingle();

      if (data == null) {
        currentUser.value = null;
        throw Exception('User not found in database');
      }

      final fetchedUser = UserModel.fromMap(data);
      currentUser.value = fetchedUser;

      // Cache selected fields only
      await DatabaseHelper.instance.insertOrUpdateUser({
        'id': fetchedUser.id,
        'name': fetchedUser.name,
        'level': fetchedUser.level,
        'streak': fetchedUser.streak,
        'profileImage': fetchedUser.profileImage,
      });
    } catch (e) {
      currentUser.value = null;
      _handleError('Failed to load user data', e);
    }
  }

  Future<void> signOut() async {
    try {
      await supabase.auth.signOut();
      await GoogleSignIn().signOut();

      user.value = null;
      currentUser.value = null;

      await SharedPrefsHelper.clear();
      await DatabaseHelper.instance.deleteUser();

      Get.offAllNamed(AppRoutes.login);
    } catch (e) {
      _handleError('Failed to sign out', e);
    }
  }

  Future<void> refreshUserData() async {
    if (user.value == null || !isConnected.value) return;

    for (int i = 0; i < 2; i++) {
      await _loadUserAndCache();
      if (currentUser.value != null) break;
      await Future.delayed(const Duration(milliseconds: 500));
    }
  }

  Future<void> checkConnectivity() async {
    try {
      final initialResult = await _connectivity.checkConnectivity();
      isConnected.value =
          initialResult.isNotEmpty &&
          !initialResult.contains(ConnectivityResult.none);
    } catch (e) {
      isConnected.value = false;
      _handleError('Connectivity check failed', e);
    }
  }

  bool get isLoggedIn => currentUser.value != null;

  Future<void> updateCachedUser(UserModel updatedUser) async {
    try {
      currentUser.value = updatedUser;
      await DatabaseHelper.instance.insertOrUpdateUser({
        'id': updatedUser.id,
        'name': updatedUser.name,
        'level': updatedUser.level,
        'streak': updatedUser.streak,
        'profileImage': updatedUser.profileImage,
      });
    } catch (e) {
      _handleError('Failed to update cached user', e);
    }
  }

  Future<void> updateStreakFromDb(int newStreak) async {
    try {
      if (currentUser.value != null) {
        final updated = currentUser.value!.copyWith(streak: newStreak);
        currentUser.value = updated;
        await DatabaseHelper.instance.insertOrUpdateUser({
          'id': updated.id,
          'name': updated.name,
          'level': updated.level,
          'streak': updated.streak,
          'profileImage': updated.profileImage,
        });
        await SharedPrefsHelper.setLastStreakUpdate(DateTime.now());
      }
    } catch (e) {
      _handleError('Failed to update streak', e);
    }
  }

  void _handleError(String title, Object error) {
    if (kDebugMode) print('$title: $error');
    showCustomSnackbar(
      title: 'Error',
      message: '$title: $error',
      isSuccess: false,
    );
  }
}
