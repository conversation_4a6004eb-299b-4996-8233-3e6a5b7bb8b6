import 'dart:convert';

class UserModel {
  final String id;
  final String email;
  final String name;
  final String? phone;
  final String? profileImage;
  final int totalAura;
  final DateTime createdAt;
  int streak;
  int exams;
  int level;
  int monthAura;

  UserModel({
    required this.id,
    required this.email,
    required this.name,
    this.phone,
    this.profileImage,
    required this.totalAura,
    required this.createdAt,
    this.streak = 0,
    this.exams = 0,
    this.level = 0,
    this.monthAura = 0,
  });

  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    String? phone,
    String? profileImage,
    int? totalAura,
    DateTime? createdAt,
    int? streak,
    int? exams,
    int? level,
    int? monthAura,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      profileImage: profileImage ?? this.profileImage,
      totalAura: totalAura ?? this.totalAura,
      createdAt: createdAt ?? this.createdAt,
      streak: streak ?? this.streak,
      exams: exams ?? this.exams,
      level: level ?? this.level,
      monthAura: monthAura ?? this.monthAura,
    );
  }

  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] ?? '',
      email: map['email'] ?? '',
      name: map['name'] ?? '',
      phone: map['phone'],
      profileImage: map['profile_image'],
      totalAura: map['total_aura'] ?? 0,
      createdAt: DateTime.parse(map['created_at']),
      streak: map['streak'] ?? 0,
      exams: map['exams'] ?? 0,
      level: map['level'] ?? 0,
      monthAura: map['month_aura'] ?? 0,
    );
  }

  /// Create a UserModel from minimal cache (SQLite)
  factory UserModel.fromCache(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] ?? '',
      email: '',
      name: map['name'] ?? '',
      phone: null,
      profileImage: map['profileImage'] ?? map['profile_image'],
      totalAura: 0,
      createdAt: DateTime.now(),
      streak: map['streak'] ?? 0,
      exams: 0,
      level: map['level'] ?? 0,
      monthAura: 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'phone': phone,
      'profile_image': profileImage,
      'total_aura': totalAura,
      'created_at': createdAt.toIso8601String(),
      'streak': streak,
      'exams': exams,
      'level': level,
      'month_aura': monthAura,
    };
  }

  String toJson() => json.encode(toMap());

  factory UserModel.fromJson(String source) =>
      UserModel.fromMap(json.decode(source));

  factory UserModel.empty() {
    return UserModel(
      id: '',
      email: '',
      name: 'Guest User',
      phone: null,
      profileImage: 'https://via.placeholder.com/150',
      totalAura: 0,
      createdAt: DateTime.now(),
      streak: 0,
      exams: 0,
      level: 0,
      monthAura: 0,
    );
  }
}
