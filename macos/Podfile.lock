PODS:
  - app_links (1.0.0):
    - FlutterMacOS
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - connectivity_plus (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - flutter_image_compress_macos (1.0.0):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 8.0)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleSignIn (8.0.0):
    - App<PERSON>uth (< 2.0, >= 1.7.3)
    - App<PERSON><PERSON>ck<PERSON>ore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - app_links (from `Flutter/ephemeral/.symlinks/plugins/app_links/macos`)
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - flutter_image_compress_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_image_compress_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - google_sign_in_ios (from `Flutter/ephemeral/.symlinks/plugins/google_sign_in_ios/darwin`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)

SPEC REPOS:
  trunk:
    - AppAuth
    - AppCheckCore
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - PromisesObjC

EXTERNAL SOURCES:
  app_links:
    :path: Flutter/ephemeral/.symlinks/plugins/app_links/macos
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  flutter_image_compress_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_image_compress_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  google_sign_in_ios:
    :path: Flutter/ephemeral/.symlinks/plugins/google_sign_in_ios/darwin
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos

SPEC CHECKSUMS:
  app_links: afe860c55c7ef176cea7fb630a2b7d7736de591d
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  connectivity_plus: 4adf20a405e25b42b9c9f87feff8f4b6fde18a4e
  file_selector_macos: 6280b52b459ae6c590af5d78fc35c7267a3c4b31
  flutter_image_compress_macos: e68daf54bb4bf2144c580fd4d151c949cbf492f0
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  google_sign_in_ios: b48bb9af78576358a168361173155596c845f0b9
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  url_launcher_macos: 0fba8ddabfc33ce0a9afe7c5fef5aab3d8d2d673

PODFILE CHECKSUM: 54d867c82ac51cbd61b565781b9fada492027009

COCOAPODS: 1.16.2
